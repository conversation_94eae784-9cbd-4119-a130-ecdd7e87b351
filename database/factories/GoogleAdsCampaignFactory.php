<?php

namespace Database\Factories;

use App\Models\GoogleAdsAccount;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GoogleAdsCampaign>
 */
class GoogleAdsCampaignFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_id' => '**********',
            'campaign_name' => $this->faker->words(3, true) . ' Campaign',
            'ad_group_name' => $this->faker->words(2, true) . ' Ad Group',
            'daily_budget_micros' => $this->faker->numberBetween(1000000, ********), // $1-$50
            'cpc_bid_micros' => $this->faker->numberBetween(100000, 5000000), // $0.10-$5.00
            'keywords' => [
                ['text' => $this->faker->word, 'match_type' => 'BROAD'],
                ['text' => $this->faker->word, 'match_type' => 'EXACT'],
                ['text' => $this->faker->words(2, true), 'match_type' => 'PHRASE'],
            ],
            'headline_1' => $this->faker->text(30),
            'headline_2' => $this->faker->text(30),
            'description' => $this->faker->text(90),
            'final_url' => $this->faker->url,
            'status' => $this->faker->randomElement(['PENDING', 'CREATED', 'FAILED']),
            'user_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the campaign is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'PENDING',
            'campaign_id' => null,
            'ad_group_id' => null,
        ]);
    }

    /**
     * Indicate that the campaign was created successfully.
     */
    public function created(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'CREATED',
            'campaign_id' => $this->faker->numerify('##########'),
            'ad_group_id' => $this->faker->numerify('##########'),
            'created_at_api' => now(),
        ]);
    }

    /**
     * Indicate that the campaign creation failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'FAILED',
            'campaign_id' => null,
            'ad_group_id' => null,
            'error_message' => 'Test error message',
        ]);
    }
}
