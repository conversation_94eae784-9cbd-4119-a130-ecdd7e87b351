<?php

namespace Database\Seeders;

use App\Models\GoogleAdsSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GoogleAdsSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'developer_token',
                'value' => null,
                'type' => 'encrypted',
                'description' => 'Google Ads API Developer Token',
                'is_encrypted' => true,
                'is_required' => true,
                'group' => 'auth',
            ],
            [
                'key' => 'login_customer_id',
                'value' => null,
                'type' => 'string',
                'description' => 'Manager Customer ID (MCC) for API access',
                'is_encrypted' => false,
                'is_required' => true,
                'group' => 'auth',
            ],
            [
                'key' => 'oauth2_client_id',
                'value' => null,
                'type' => 'encrypted',
                'description' => 'OAuth2 Client ID for Google Ads API',
                'is_encrypted' => true,
                'is_required' => true,
                'group' => 'auth',
            ],
            [
                'key' => 'oauth2_client_secret',
                'value' => null,
                'type' => 'encrypted',
                'description' => 'OAuth2 Client Secret for Google Ads API',
                'is_encrypted' => true,
                'is_required' => true,
                'group' => 'auth',
            ],
            [
                'key' => 'oauth2_refresh_token',
                'value' => null,
                'type' => 'encrypted',
                'description' => 'OAuth2 Refresh Token for Google Ads API',
                'is_encrypted' => true,
                'is_required' => true,
                'group' => 'auth',
            ],
            [
                'key' => 'api_version',
                'value' => 'v20',
                'type' => 'string',
                'description' => 'Google Ads API Version',
                'is_encrypted' => false,
                'is_required' => false,
                'group' => 'api',
            ],
            [
                'key' => 'auto_sync_enabled',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Enable automatic account synchronization',
                'is_encrypted' => false,
                'is_required' => false,
                'group' => 'general',
            ],
            [
                'key' => 'sync_interval_hours',
                'value' => '24',
                'type' => 'integer',
                'description' => 'Hours between automatic synchronizations',
                'is_encrypted' => false,
                'is_required' => false,
                'group' => 'general',
            ],
            [
                'key' => 'default_currency',
                'value' => 'USD',
                'type' => 'string',
                'description' => 'Default currency for new accounts',
                'is_encrypted' => false,
                'is_required' => false,
                'group' => 'general',
            ],
            [
                'key' => 'default_timezone',
                'value' => 'America/New_York',
                'type' => 'string',
                'description' => 'Default timezone for new accounts',
                'is_encrypted' => false,
                'is_required' => false,
                'group' => 'general',
            ],
        ];

        foreach ($settings as $setting) {
            GoogleAdsSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
