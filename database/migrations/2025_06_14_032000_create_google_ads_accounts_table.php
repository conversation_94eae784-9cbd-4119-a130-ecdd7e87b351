<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_ads_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('customer_id')->unique()->index(); // Google Ads Customer ID
            $table->string('name'); // Account name
            $table->string('descriptive_name')->nullable(); // Descriptive name
            $table->boolean('is_manager_account')->default(false); // Is MCC account
            $table->string('status')->default('ENABLED'); // ENABLED, PAUSED, REMOVED
            $table->string('currency_code', 3)->nullable(); // Currency code (USD, EUR, etc.)
            $table->string('time_zone')->nullable(); // Account timezone
            $table->string('manager_customer_id')->nullable()->index(); // Parent MCC ID
            $table->boolean('can_manage_clients')->default(false); // Can manage other accounts
            $table->boolean('test_account')->default(false); // Is test account
            $table->json('optimization_score')->nullable(); // Optimization score data
            $table->timestamp('last_synced_at')->nullable(); // Last sync with Google Ads API
            $table->json('raw_data')->nullable(); // Store raw API response
            $table->timestamps();

            // Foreign key constraint for manager account
            $table->foreign('manager_customer_id')
                  ->references('customer_id')
                  ->on('google_ads_accounts')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_ads_accounts');
    }
};
