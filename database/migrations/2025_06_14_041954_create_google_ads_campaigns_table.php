<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_ads_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('customer_id')->index(); // Google Ads Customer ID
            $table->string('campaign_id')->nullable()->index(); // Google Ads Campaign ID (after creation)
            $table->string('campaign_name'); // Campaign name
            $table->string('ad_group_name'); // Ad group name
            $table->string('ad_group_id')->nullable(); // Google Ads Ad Group ID (after creation)
            $table->decimal('daily_budget_micros', 15, 0); // Daily budget in micros
            $table->decimal('cpc_bid_micros', 15, 0); // CPC bid in micros
            $table->json('keywords'); // Keywords with match types
            $table->string('headline_1', 30); // First headline
            $table->string('headline_2', 30); // Second headline
            $table->string('description', 90); // Ad description
            $table->string('final_url'); // Landing page URL
            $table->string('status')->default('PENDING'); // PENDING, CREATED, FAILED
            $table->text('error_message')->nullable(); // Error message if failed
            $table->json('api_response')->nullable(); // Store API response data
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // User who created
            $table->timestamp('created_at_api')->nullable(); // When created in Google Ads
            $table->timestamps();

            // Foreign key to GoogleAdsAccount
            $table->foreign('customer_id')
                  ->references('customer_id')
                  ->on('google_ads_accounts')
                  ->onDelete('cascade');

            // Indexes for better performance
            $table->index(['customer_id', 'status']);
            $table->index(['campaign_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_ads_campaigns');
    }
};
