<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // User who performed action
            $table->string('action'); // Action performed (create, update, delete, sync, etc.)
            $table->string('model_type'); // Model class name
            $table->string('model_id')->nullable(); // Model ID
            $table->string('customer_id')->nullable()->index(); // Google Ads Customer ID if applicable
            $table->text('description'); // Human-readable description
            $table->json('old_values')->nullable(); // Previous values (for updates)
            $table->json('new_values')->nullable(); // New values
            $table->json('metadata')->nullable(); // Additional metadata
            $table->string('ip_address')->nullable(); // User's IP address
            $table->string('user_agent')->nullable(); // User's browser/client
            $table->string('status')->default('success'); // success, failed, pending
            $table->text('error_message')->nullable(); // Error message if failed
            $table->timestamps();

            // Indexes for better performance
            $table->index(['model_type', 'model_id']);
            $table->index(['action', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
