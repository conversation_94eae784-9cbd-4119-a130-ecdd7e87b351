<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_ads_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Setting key (e.g., 'developer_token', 'login_customer_id')
            $table->text('value')->nullable(); // Setting value (encrypted for sensitive data)
            $table->string('type')->default('string'); // string, integer, boolean, json, encrypted
            $table->text('description')->nullable(); // Human-readable description
            $table->boolean('is_encrypted')->default(false); // Whether value is encrypted
            $table->boolean('is_required')->default(false); // Whether setting is required
            $table->string('group')->default('general'); // Group settings (general, auth, api, etc.)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_ads_settings');
    }
};
