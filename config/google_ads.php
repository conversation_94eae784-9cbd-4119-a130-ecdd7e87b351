<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Google Ads API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Google Ads API integration. These settings can be
    | overridden by database settings managed through the admin panel.
    |
    */

    'developer_token' => env('GOOGLE_ADS_DEVELOPER_TOKEN'),
    'login_customer_id' => env('GOOGLE_ADS_LOGIN_CUSTOMER_ID'),
    
    'oauth2' => [
        'client_id' => env('GOOGLE_ADS_OAUTH2_CLIENT_ID'),
        'client_secret' => env('GOOGLE_ADS_OAUTH2_CLIENT_SECRET'),
        'refresh_token' => env('GOOGLE_ADS_OAUTH2_REFRESH_TOKEN'),
    ],

    'api' => [
        'version' => env('GOOGLE_ADS_API_VERSION', 'v20'),
        'endpoint' => env('GOOGLE_ADS_API_ENDPOINT', 'https://googleads.googleapis.com'),
    ],

    'sync' => [
        'auto_enabled' => env('GOOGLE_ADS_AUTO_SYNC', false),
        'interval_hours' => env('GOOGLE_ADS_SYNC_INTERVAL', 24),
    ],

    'defaults' => [
        'currency' => env('GOOGLE_ADS_DEFAULT_CURRENCY', 'USD'),
        'timezone' => env('GOOGLE_ADS_DEFAULT_TIMEZONE', 'America/New_York'),
    ],

    'logging' => [
        'enabled' => env('GOOGLE_ADS_LOGGING_ENABLED', true),
        'level' => env('GOOGLE_ADS_LOG_LEVEL', 'info'),
        'channel' => env('GOOGLE_ADS_LOG_CHANNEL', 'single'),
    ],
];
