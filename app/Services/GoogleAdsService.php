<?php

namespace App\Services;

use App\Models\GoogleAdsAccount;
use App\Models\GoogleAdsSetting;
use App\Models\GoogleAdsCampaign;
use App\Models\AuditLog;
use App\Repositories\GoogleAdsRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class GoogleAdsService
{
    public function __construct(
        protected GoogleAdsRepository $repository
    ) {}

    /**
     * Sync all child accounts from Google Ads API
     */
    public function syncChildAccounts(?string $managerCustomerId = null): Collection
    {
        try {
            $managerCustomerId = $managerCustomerId ?? GoogleAdsSetting::getValue('login_customer_id');
            
            if (!$managerCustomerId) {
                throw new \Exception('Manager Customer ID not configured');
            }

            $accounts = $this->repository->getChildAccounts($managerCustomerId);
            $syncedAccounts = collect();

            foreach ($accounts as $accountData) {
                $account = $this->createOrUpdateAccount($accountData);
                $syncedAccounts->push($account);
            }

            AuditLog::logAction(
                'sync_accounts',
                "Synced {$syncedAccounts->count()} accounts from Google Ads API",
                'GoogleAdsService',
                'sync_child_accounts',
                $managerCustomerId,
                null,
                ['synced_count' => $syncedAccounts->count()]
            );

            return $syncedAccounts;
        } catch (\Exception $e) {
            Log::error('Failed to sync Google Ads accounts', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId
            ]);

            AuditLog::logAction(
                'sync_accounts',
                'Failed to sync accounts from Google Ads API',
                'GoogleAdsService',
                'sync_child_accounts',
                $managerCustomerId,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Create a new Google Ads account
     */
    public function createAccount(array $data): GoogleAdsAccount
    {
        try {
            $accountData = $this->repository->createAccount($data);
            $account = $this->createOrUpdateAccount($accountData);

            AuditLog::logAction(
                'create_account',
                "Created Google Ads account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                $account->toArray()
            );

            return $account;
        } catch (\Exception $e) {
            Log::error('Failed to create Google Ads account', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            AuditLog::logAction(
                'create_account',
                'Failed to create Google Ads account',
                'GoogleAdsService',
                'create_account',
                null,
                null,
                $data,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Disable account by unlinking from manager
     */
    public function disableAccount(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            
            if (!$account->manager_customer_id) {
                throw new \Exception('Account is not linked to a manager account');
            }

            $result = $this->repository->unlinkClientFromManager($managerCustomerId, $account->customer_id);
            
            $account->update(['status' => 'PAUSED']);

            AuditLog::logAction(
                'disable_account',
                "Disabled account by unlinking from manager: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                ['status' => 'ENABLED'],
                ['status' => 'PAUSED']
            );

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to disable Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);

            AuditLog::logAction(
                'disable_account',
                "Failed to disable account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Enable account by linking to manager
     */
    public function enableAccount(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            
            $result = $this->repository->linkClientToManager($managerCustomerId, $account->customer_id);
            
            $account->update(['status' => 'ENABLED']);

            AuditLog::logAction(
                'enable_account',
                "Enabled account by linking to manager: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                ['status' => 'PAUSED'],
                ['status' => 'ENABLED']
            );

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to enable Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);

            AuditLog::logAction(
                'enable_account',
                "Failed to enable account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Check if account is linked to manager
     */
    public function isAccountLinked(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            return $this->repository->isClientLinkedToManager($managerCustomerId, $account->customer_id);
        } catch (\Exception $e) {
            Log::error('Failed to check account link status', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);
            return false;
        }
    }

    /**
     * Simulate user invitation (Google Ads API doesn't support direct invitations)
     */
    public function inviteUser(GoogleAdsAccount $account, string $email, string $role = 'STANDARD'): bool
    {
        try {
            AuditLog::logAction(
                'invite_user',
                "Simulated user invitation for {$email} to account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                ['email' => $email, 'role' => $role]
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to invite user to Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id,
                'email' => $email
            ]);

            AuditLog::logAction(
                'invite_user',
                "Failed to invite user {$email} to account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                ['email' => $email, 'role' => $role],
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Get MCC hierarchy tree
     */
    public function getMccHierarchy(?string $rootCustomerId = null): Collection
    {
        $rootCustomerId = $rootCustomerId ?? GoogleAdsSetting::getValue('login_customer_id');
        
        return GoogleAdsAccount::with('descendants')
            ->where('customer_id', $rootCustomerId)
            ->orWhere('manager_customer_id', $rootCustomerId)
            ->get();
    }

    /**
     * Create or update account from API data
     */
    protected function createOrUpdateAccount(array $data): GoogleAdsAccount
    {
        return GoogleAdsAccount::updateOrCreate(
            ['customer_id' => $data['customer_id']],
            array_merge($data, ['last_synced_at' => now()])
        );
    }

    /**
     * Check if Google Ads API is properly configured
     */
    public function isConfigured(): bool
    {
        $requiredSettings = [
            'developer_token',
            'login_customer_id',
            'oauth2_client_id',
            'oauth2_client_secret',
            'oauth2_refresh_token'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty(GoogleAdsSetting::getValue($setting))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get configuration status
     */
    public function getConfigurationStatus(): array
    {
        $requiredSettings = [
            'developer_token' => 'Developer Token',
            'login_customer_id' => 'Login Customer ID',
            'oauth2_client_id' => 'OAuth2 Client ID',
            'oauth2_client_secret' => 'OAuth2 Client Secret',
            'oauth2_refresh_token' => 'OAuth2 Refresh Token'
        ];

        $status = [];
        foreach ($requiredSettings as $key => $label) {
            $status[$key] = [
                'label' => $label,
                'configured' => !empty(GoogleAdsSetting::getValue($key)),
                'value' => GoogleAdsSetting::getValue($key) ? '***' : null
            ];
        }

        return $status;
    }

    /**
     * Create a complete Google Ads Search campaign
     */
    public function createSearchCampaign(array $data): GoogleAdsCampaign
    {
        // Validate required fields
        $this->validateCampaignData($data);

        // Create campaign record in database first
        $campaign = GoogleAdsCampaign::create([
            'customer_id' => $data['customer_id'],
            'campaign_name' => $data['campaign_name'],
            'ad_group_name' => $data['ad_group_name'],
            'daily_budget_micros' => $data['budget_amount'] * 1000000,
            'cpc_bid_micros' => $data['cpc_bid'] * 1000000,
            'keywords' => $this->parseKeywords($data['keywords']),
            'headline_1' => $data['headline_1'],
            'headline_2' => $data['headline_2'],
            'description' => $data['description'],
            'final_url' => $data['final_url'],
            'status' => 'PENDING',
            'user_id' => Auth::id(),
        ]);

        try {
            // Create campaign through Google Ads API
            $result = $this->repository->createSearchCampaign([
                'customer_id' => $data['customer_id'],
                'campaign_name' => $data['campaign_name'],
                'ad_group_name' => $data['ad_group_name'],
                'daily_budget_micros' => $data['budget_amount'] * 1000000,
                'cpc_bid_micros' => $data['cpc_bid'] * 1000000,
                'keywords' => $this->parseKeywords($data['keywords']),
                'headline_1' => $data['headline_1'],
                'headline_2' => $data['headline_2'],
                'description' => $data['description'],
                'final_url' => $data['final_url'],
            ]);

            // Update campaign with API response
            $campaign->update([
                'campaign_id' => $result['campaign_id'],
                'ad_group_id' => $result['ad_group_id'],
                'status' => 'CREATED',
                'api_response' => $result,
                'created_at_api' => now(),
            ]);

            AuditLog::logAction(
                'create_search_campaign',
                "Created search campaign: {$campaign->campaign_name}",
                GoogleAdsCampaign::class,
                $campaign->id,
                $campaign->customer_id,
                null,
                $campaign->toArray()
            );

            return $campaign;

        } catch (\Exception $e) {
            // Update campaign with error
            $campaign->update([
                'status' => 'FAILED',
                'error_message' => $e->getMessage(),
            ]);

            Log::error('Failed to create Google Ads search campaign', [
                'error' => $e->getMessage(),
                'campaign_id' => $campaign->id,
                'customer_id' => $data['customer_id']
            ]);

            AuditLog::logAction(
                'create_search_campaign',
                "Failed to create search campaign: {$campaign->campaign_name}",
                GoogleAdsCampaign::class,
                $campaign->id,
                $campaign->customer_id,
                null,
                $campaign->toArray(),
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Validate campaign creation data
     */
    protected function validateCampaignData(array $data): void
    {
        $errors = [];

        // Required fields
        $required = ['customer_id', 'campaign_name', 'ad_group_name', 'budget_amount', 'cpc_bid', 'keywords', 'headline_1', 'headline_2', 'description', 'final_url'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field {$field} is required";
            }
        }

        // Character limits
        if (!empty($data['campaign_name']) && strlen($data['campaign_name']) > 255) {
            $errors[] = 'Campaign name must be 255 characters or less';
        }

        if (!empty($data['ad_group_name']) && strlen($data['ad_group_name']) > 255) {
            $errors[] = 'Ad group name must be 255 characters or less';
        }

        if (!empty($data['headline_1']) && strlen($data['headline_1']) > 30) {
            $errors[] = 'Headline 1 must be 30 characters or less';
        }

        if (!empty($data['headline_2']) && strlen($data['headline_2']) > 30) {
            $errors[] = 'Headline 2 must be 30 characters or less';
        }

        if (!empty($data['description']) && strlen($data['description']) > 90) {
            $errors[] = 'Description must be 90 characters or less';
        }

        // Numeric validations
        if (!empty($data['budget_amount']) && ($data['budget_amount'] < 1.00 || !is_numeric($data['budget_amount']))) {
            $errors[] = 'Budget amount must be at least $1.00';
        }

        if (!empty($data['cpc_bid']) && ($data['cpc_bid'] < 0.01 || !is_numeric($data['cpc_bid']))) {
            $errors[] = 'CPC bid must be at least $0.01';
        }

        // URL validation
        if (!empty($data['final_url']) && !filter_var($data['final_url'], FILTER_VALIDATE_URL)) {
            $errors[] = 'Final URL must be a valid URL';
        }

        if (!empty($data['final_url']) && !in_array(parse_url($data['final_url'], PHP_URL_SCHEME), ['http', 'https'])) {
            $errors[] = 'Final URL must use HTTP or HTTPS protocol';
        }

        // Customer ID validation
        if (!empty($data['customer_id']) && !GoogleAdsAccount::where('customer_id', $data['customer_id'])->exists()) {
            $errors[] = 'Invalid customer ID';
        }

        if (!empty($errors)) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $errors));
        }
    }

    /**
     * Parse keywords string into structured array
     */
    protected function parseKeywords($keywordsInput): array
    {
        if (is_array($keywordsInput)) {
            return $keywordsInput;
        }

        $keywords = [];
        $lines = explode("\n", $keywordsInput);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Parse different match type formats
            if (preg_match('/^\[(.+)\]$/', $line, $matches)) {
                // [exact match]
                $keywords[] = [
                    'text' => trim($matches[1]),
                    'match_type' => 'EXACT'
                ];
            } elseif (preg_match('/^"(.+)"$/', $line, $matches)) {
                // "phrase match"
                $keywords[] = [
                    'text' => trim($matches[1]),
                    'match_type' => 'PHRASE'
                ];
            } elseif (preg_match('/^\+(.+)$/', $line, $matches)) {
                // +broad match modifier
                $keywords[] = [
                    'text' => trim($matches[1]),
                    'match_type' => 'BROAD_MODIFIED'
                ];
            } else {
                // broad match (default)
                $keywords[] = [
                    'text' => $line,
                    'match_type' => 'BROAD'
                ];
            }
        }

        return $keywords;
    }

    /**
     * Sync campaigns from Google Ads API to local database
     */
    public function syncCampaignsFromApi(string $customerId): Collection
    {
        try {
            // Get campaigns from Google Ads API
            $apiCampaigns = $this->repository->syncCampaigns($customerId);
            $syncedCampaigns = collect();

            foreach ($apiCampaigns as $campaignData) {
                // Try to find existing campaign in database
                $existingCampaign = GoogleAdsCampaign::where('campaign_id', $campaignData['campaign_id'])
                    ->where('customer_id', $customerId)
                    ->first();

                if ($existingCampaign) {
                    // Update existing campaign
                    $existingCampaign->update([
                        'campaign_name' => $campaignData['campaign_name'],
                        'status' => $this->mapApiStatusToLocal($campaignData['status']),
                        'daily_budget_micros' => $campaignData['daily_budget_micros'],
                        'api_response' => $campaignData,
                        'last_synced_at' => now(),
                    ]);
                    $syncedCampaigns->push($existingCampaign);
                } else {
                    // Get detailed campaign information including ad groups
                    $campaignDetails = $this->repository->getCampaignDetails($customerId, $campaignData['campaign_id']);

                    // Create new campaign record from API data
                    $newCampaign = $this->createCampaignFromApiData($customerId, $campaignDetails);
                    $syncedCampaigns->push($newCampaign);
                }
            }

            AuditLog::logAction(
                'sync_campaigns_from_api',
                "Synced {$syncedCampaigns->count()} campaigns from Google Ads API",
                'GoogleAdsService',
                'sync_campaigns_from_api',
                $customerId,
                null,
                ['synced_count' => $syncedCampaigns->count()]
            );

            return $syncedCampaigns;

        } catch (\Exception $e) {
            Log::error('Failed to sync campaigns from Google Ads API', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);

            AuditLog::logAction(
                'sync_campaigns_from_api',
                'Failed to sync campaigns from Google Ads API',
                'GoogleAdsService',
                'sync_campaigns_from_api',
                $customerId,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Create campaign record from Google Ads API data
     */
    protected function createCampaignFromApiData(string $customerId, array $campaignDetails): GoogleAdsCampaign
    {
        // Extract first ad group data for campaign creation
        $firstAdGroup = $campaignDetails['ad_groups'][0] ?? null;
        $firstAd = $firstAdGroup['ads'][0] ?? null;
        $keywords = $firstAdGroup['keywords'] ?? [];

        // Format keywords for storage
        $formattedKeywords = collect($keywords)->map(function ($keyword) {
            return [
                'text' => $keyword['text'],
                'match_type' => $keyword['match_type']
            ];
        })->toArray();

        return GoogleAdsCampaign::create([
            'customer_id' => $customerId,
            'campaign_id' => $campaignDetails['campaign_id'],
            'campaign_name' => $campaignDetails['campaign_name'],
            'ad_group_name' => $firstAdGroup['ad_group_name'] ?? 'Default Ad Group',
            'ad_group_id' => $firstAdGroup['ad_group_id'] ?? null,
            'daily_budget_micros' => $campaignDetails['daily_budget_micros'],
            'cpc_bid_micros' => $firstAdGroup['cpc_bid_micros'] ?? 0,
            'keywords' => $formattedKeywords,
            'headline_1' => $firstAd['headlines'][0] ?? 'Headline 1',
            'headline_2' => $firstAd['headlines'][1] ?? 'Headline 2',
            'description' => $firstAd['descriptions'][0] ?? 'Description',
            'final_url' => $firstAd['final_urls'][0] ?? 'https://example.com',
            'status' => 'CREATED',
            'api_response' => $campaignDetails,
            'created_at_api' => now(),
            'user_id' => Auth::id(),
        ]);
    }

    /**
     * Map Google Ads API status to local status
     */
    protected function mapApiStatusToLocal(string $apiStatus): string
    {
        return match ($apiStatus) {
            'ENABLED' => 'CREATED',
            'PAUSED' => 'CREATED',
            'REMOVED' => 'FAILED',
            default => 'CREATED',
        };
    }

    /**
     * Sync specific campaign details
     */
    public function syncCampaignDetails(string $customerId, string $campaignId): array
    {
        try {
            $campaignDetails = $this->repository->getCampaignDetails($customerId, $campaignId);

            AuditLog::logAction(
                'sync_campaign_details',
                "Synced details for campaign {$campaignId}",
                'GoogleAdsService',
                'sync_campaign_details',
                $customerId,
                null,
                ['campaign_id' => $campaignId]
            );

            return $campaignDetails;

        } catch (\Exception $e) {
            Log::error('Failed to sync campaign details', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'campaign_id' => $campaignId
            ]);

            AuditLog::logAction(
                'sync_campaign_details',
                "Failed to sync details for campaign {$campaignId}",
                'GoogleAdsService',
                'sync_campaign_details',
                $customerId,
                null,
                ['campaign_id' => $campaignId],
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Update local campaign with latest data from Google Ads
     */
    public function updateCampaignFromApi(GoogleAdsCampaign $campaign): GoogleAdsCampaign
    {
        try {
            if (!$campaign->campaign_id) {
                throw new \Exception('Campaign does not have a Google Ads campaign ID');
            }

            $campaignDetails = $this->repository->getCampaignDetails(
                $campaign->customer_id,
                $campaign->campaign_id
            );

            // Update campaign with latest data
            $campaign->update([
                'campaign_name' => $campaignDetails['campaign_name'],
                'status' => $this->mapApiStatusToLocal($campaignDetails['status']),
                'daily_budget_micros' => $campaignDetails['daily_budget_micros'],
                'api_response' => $campaignDetails,
                'last_synced_at' => now(),
            ]);

            AuditLog::logAction(
                'update_campaign_from_api',
                "Updated campaign {$campaign->campaign_name} from Google Ads API",
                GoogleAdsCampaign::class,
                $campaign->id,
                $campaign->customer_id,
                null,
                $campaign->toArray()
            );

            return $campaign;

        } catch (\Exception $e) {
            Log::error('Failed to update campaign from API', [
                'error' => $e->getMessage(),
                'campaign_id' => $campaign->id,
                'google_campaign_id' => $campaign->campaign_id
            ]);

            AuditLog::logAction(
                'update_campaign_from_api',
                "Failed to update campaign {$campaign->campaign_name} from Google Ads API",
                GoogleAdsCampaign::class,
                $campaign->id,
                $campaign->customer_id,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }
}
