<?php

namespace App\Services;

use App\Models\GoogleAdsAccount;
use App\Models\GoogleAdsSetting;
use App\Models\AuditLog;
use App\Repositories\GoogleAdsRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GoogleAdsService
{
    public function __construct(
        protected GoogleAdsRepository $repository
    ) {}

    /**
     * Sync all child accounts from Google Ads API
     */
    public function syncChildAccounts(?string $managerCustomerId = null): Collection
    {
        try {
            $managerCustomerId = $managerCustomerId ?? GoogleAdsSetting::getValue('login_customer_id');
            
            if (!$managerCustomerId) {
                throw new \Exception('Manager Customer ID not configured');
            }

            $accounts = $this->repository->getChildAccounts($managerCustomerId);
            $syncedAccounts = collect();

            foreach ($accounts as $accountData) {
                $account = $this->createOrUpdateAccount($accountData);
                $syncedAccounts->push($account);
            }

            AuditLog::logAction(
                'sync_accounts',
                "Synced {$syncedAccounts->count()} accounts from Google Ads API",
                'GoogleAdsService',
                'sync_child_accounts',
                $managerCustomerId,
                null,
                ['synced_count' => $syncedAccounts->count()]
            );

            return $syncedAccounts;
        } catch (\Exception $e) {
            Log::error('Failed to sync Google Ads accounts', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId
            ]);

            AuditLog::logAction(
                'sync_accounts',
                'Failed to sync accounts from Google Ads API',
                'GoogleAdsService',
                'sync_child_accounts',
                $managerCustomerId,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Create a new Google Ads account
     */
    public function createAccount(array $data): GoogleAdsAccount
    {
        try {
            $accountData = $this->repository->createAccount($data);
            $account = $this->createOrUpdateAccount($accountData);

            AuditLog::logAction(
                'create_account',
                "Created Google Ads account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                $account->toArray()
            );

            return $account;
        } catch (\Exception $e) {
            Log::error('Failed to create Google Ads account', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            AuditLog::logAction(
                'create_account',
                'Failed to create Google Ads account',
                'GoogleAdsService',
                'create_account',
                null,
                null,
                $data,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Disable account by unlinking from manager
     */
    public function disableAccount(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            
            if (!$account->manager_customer_id) {
                throw new \Exception('Account is not linked to a manager account');
            }

            $result = $this->repository->unlinkClientFromManager($managerCustomerId, $account->customer_id);
            
            $account->update(['status' => 'PAUSED']);

            AuditLog::logAction(
                'disable_account',
                "Disabled account by unlinking from manager: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                ['status' => 'ENABLED'],
                ['status' => 'PAUSED']
            );

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to disable Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);

            AuditLog::logAction(
                'disable_account',
                "Failed to disable account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Enable account by linking to manager
     */
    public function enableAccount(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            
            $result = $this->repository->linkClientToManager($managerCustomerId, $account->customer_id);
            
            $account->update(['status' => 'ENABLED']);

            AuditLog::logAction(
                'enable_account',
                "Enabled account by linking to manager: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                ['status' => 'PAUSED'],
                ['status' => 'ENABLED']
            );

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to enable Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);

            AuditLog::logAction(
                'enable_account',
                "Failed to enable account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                null,
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Check if account is linked to manager
     */
    public function isAccountLinked(GoogleAdsAccount $account): bool
    {
        try {
            $managerCustomerId = GoogleAdsSetting::getValue('login_customer_id');
            return $this->repository->isClientLinkedToManager($managerCustomerId, $account->customer_id);
        } catch (\Exception $e) {
            Log::error('Failed to check account link status', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id
            ]);
            return false;
        }
    }

    /**
     * Simulate user invitation (Google Ads API doesn't support direct invitations)
     */
    public function inviteUser(GoogleAdsAccount $account, string $email, string $role = 'STANDARD'): bool
    {
        try {
            AuditLog::logAction(
                'invite_user',
                "Simulated user invitation for {$email} to account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                ['email' => $email, 'role' => $role]
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to invite user to Google Ads account', [
                'error' => $e->getMessage(),
                'customer_id' => $account->customer_id,
                'email' => $email
            ]);

            AuditLog::logAction(
                'invite_user',
                "Failed to invite user {$email} to account: {$account->name}",
                GoogleAdsAccount::class,
                $account->id,
                $account->customer_id,
                null,
                ['email' => $email, 'role' => $role],
                ['error' => $e->getMessage()],
                'failed',
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Get MCC hierarchy tree
     */
    public function getMccHierarchy(?string $rootCustomerId = null): Collection
    {
        $rootCustomerId = $rootCustomerId ?? GoogleAdsSetting::getValue('login_customer_id');
        
        return GoogleAdsAccount::with('descendants')
            ->where('customer_id', $rootCustomerId)
            ->orWhere('manager_customer_id', $rootCustomerId)
            ->get();
    }

    /**
     * Create or update account from API data
     */
    protected function createOrUpdateAccount(array $data): GoogleAdsAccount
    {
        return GoogleAdsAccount::updateOrCreate(
            ['customer_id' => $data['customer_id']],
            array_merge($data, ['last_synced_at' => now()])
        );
    }

    /**
     * Check if Google Ads API is properly configured
     */
    public function isConfigured(): bool
    {
        $requiredSettings = [
            'developer_token',
            'login_customer_id',
            'oauth2_client_id',
            'oauth2_client_secret',
            'oauth2_refresh_token'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty(GoogleAdsSetting::getValue($setting))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get configuration status
     */
    public function getConfigurationStatus(): array
    {
        $requiredSettings = [
            'developer_token' => 'Developer Token',
            'login_customer_id' => 'Login Customer ID',
            'oauth2_client_id' => 'OAuth2 Client ID',
            'oauth2_client_secret' => 'OAuth2 Client Secret',
            'oauth2_refresh_token' => 'OAuth2 Refresh Token'
        ];

        $status = [];
        foreach ($requiredSettings as $key => $label) {
            $status[$key] = [
                'label' => $label,
                'configured' => !empty(GoogleAdsSetting::getValue($key)),
                'value' => GoogleAdsSetting::getValue($key) ? '***' : null
            ];
        }

        return $status;
    }
}
