<?php

namespace App\Repositories;

use App\Models\GoogleAdsSetting;
use Google\Ads\GoogleAds\Lib\V20\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V20\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\V20\Services\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V20\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V20\Services\CampaignServiceClient;
use Google\Ads\GoogleAds\V20\Resources\Customer;
use Google\Ads\GoogleAds\V20\Resources\Campaign;
use Google\Ads\GoogleAds\V20\Enums\CustomerStatusEnum\CustomerStatus;
use Google\Ads\GoogleAds\V20\Enums\CampaignStatusEnum\CampaignStatus;
use Google\Ads\GoogleAds\V20\Services\CreateCustomerClientRequest;
use Google\Ads\GoogleAds\V20\Services\MutateCampaignsRequest;
use Google\Ads\GoogleAds\V20\Services\SearchGoogleAdsRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GoogleAdsRepository
{
    protected GoogleAdsClient $client;

    public function __construct()
    {
        $this->initializeClient();
    }

    /**
     * Initialize Google Ads client
     */
    protected function initializeClient(): void
    {
        try {
            $oAuth2Credential = (new OAuth2TokenBuilder())
                ->withClientId(GoogleAdsSetting::getValue('oauth2_client_id'))
                ->withClientSecret(GoogleAdsSetting::getValue('oauth2_client_secret'))
                ->withRefreshToken(GoogleAdsSetting::getValue('oauth2_refresh_token'))
                ->build();

            $this->client = (new GoogleAdsClientBuilder())
                ->withOAuth2Credential($oAuth2Credential)
                ->withDeveloperToken(GoogleAdsSetting::getValue('developer_token'))
                ->withLoginCustomerId(GoogleAdsSetting::getValue('login_customer_id'))
                ->build();
        } catch (\Exception $e) {
            Log::error('Failed to initialize Google Ads client', ['error' => $e->getMessage()]);
            throw new \Exception('Google Ads client initialization failed: ' . $e->getMessage());
        }
    }

    /**
     * Get child accounts using GAQL
     */
    public function getChildAccounts(string $managerCustomerId): array
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();
            
            $query = "SELECT 
                customer_client.client_customer,
                customer_client.level,
                customer_client.manager,
                customer_client.descriptive_name,
                customer_client.currency_code,
                customer_client.time_zone,
                customer_client.id,
                customer_client.status,
                customer_client.test_account
                FROM customer_client 
                WHERE customer_client.level <= 1";

            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($managerCustomerId, $query)
            );
            
            $accounts = [];
            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $customerClient = $googleAdsRow->getCustomerClient();
                
                $accounts[] = [
                    'customer_id' => $customerClient->getId(),
                    'name' => $customerClient->getDescriptiveName() ?: 'Unnamed Account',
                    'descriptive_name' => $customerClient->getDescriptiveName(),
                    'is_manager_account' => $customerClient->getManager(),
                    'status' => $this->mapCustomerStatus($customerClient->getStatus()),
                    'currency_code' => $customerClient->getCurrencyCode(),
                    'time_zone' => $customerClient->getTimeZone(),
                    'manager_customer_id' => $customerClient->getManager() ? null : $managerCustomerId,
                    'can_manage_clients' => $customerClient->getManager(),
                    'test_account' => $customerClient->getTestAccount(),
                    'raw_data' => [
                        'level' => $customerClient->getLevel(),
                        'client_customer' => $customerClient->getClientCustomer(),
                    ]
                ];
            }

            return $accounts;
        } catch (\Exception $e) {
            Log::error('Failed to get child accounts', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId
            ]);
            throw $e;
        }
    }

    /**
     * Create a new customer account
     */
    public function createAccount(array $data): array
    {
        try {
            $customerService = $this->client->getCustomerServiceClient();
            
            $customer = new Customer([
                'descriptive_name' => $data['name'],
                'currency_code' => $data['currency_code'] ?? 'USD',
                'time_zone' => $data['time_zone'] ?? 'America/New_York',
            ]);

            $response = $customerService->createCustomerClient(
                CreateCustomerClientRequest::build(
                    GoogleAdsSetting::getValue('login_customer_id'),
                    $customer
                ));

            // Get the created customer ID from the response
            $customerId = $response->getResourceName();
            $customerId = substr($customerId, strrpos($customerId, '/') + 1);

            return [
                'customer_id' => $customerId,
                'name' => $data['name'],
                'descriptive_name' => $data['name'],
                'is_manager_account' => $data['is_manager_account'] ?? false,
                'status' => 'ENABLED',
                'currency_code' => $data['currency_code'] ?? 'USD',
                'time_zone' => $data['time_zone'] ?? 'America/New_York',
                'manager_customer_id' => GoogleAdsSetting::getValue('login_customer_id'),
                'can_manage_clients' => $data['is_manager_account'] ?? false,
                'test_account' => $data['test_account'] ?? false,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create customer account', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Pause all campaigns in an account
     */
    public function pauseAllCampaigns(string $customerId): bool
    {
        try {
            return $this->updateAllCampaignsStatus($customerId, CampaignStatus::PAUSED);
        } catch (\Exception $e) {
            Log::error('Failed to pause campaigns', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }

    /**
     * Enable all campaigns in an account
     */
    public function enableAllCampaigns(string $customerId): bool
    {
        try {
            return $this->updateAllCampaignsStatus($customerId, CampaignStatus::ENABLED);
        } catch (\Exception $e) {
            Log::error('Failed to enable campaigns', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }

    /**
     * Update all campaigns status
     */
    protected function updateAllCampaignsStatus(string $customerId, int $status): bool
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();
            
            // First, get all campaigns
            $query = "SELECT campaign.id, campaign.name FROM campaign WHERE campaign.status != 'REMOVED'";
            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );
            
            $campaignService = $this->client->getCampaignServiceClient();
            $operations = [];
            
            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $campaign = $googleAdsRow->getCampaign();
                
                $campaignUpdate = new Campaign([
                    'resource_name' => $campaign->getResourceName(),
                    'status' => $status
                ]);
                
                $operations[] = $campaignService->newCampaignOperation()
                    ->setUpdate($campaignUpdate)
                    ->setUpdateMask(['status']);
            }
            
            if (!empty($operations)) {
                $campaignService->mutateCampaigns(
                    MutateCampaignsRequest::build($customerId, $operations)
                );
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update campaigns status', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'status' => $status
            ]);
            throw $e;
        }
    }

    /**
     * Map Google Ads customer status to our status
     */
    protected function mapCustomerStatus(?int $status): string
    {
        return match ($status) {
            CustomerStatus::ENABLED => 'ENABLED',
            CustomerStatus::CANCELED => 'REMOVED',
            CustomerStatus::SUSPENDED => 'PAUSED',
            default => 'ENABLED',
        };
    }

    /**
     * Test API connection
     */
    public function testConnection(): bool
    {
        try {
            $customerService = $this->client->getCustomerServiceClient();
            $customerId = GoogleAdsSetting::getValue('login_customer_id');
            
            $googleAdsService = $this->client->getGoogleAdsServiceClient();
            $query = "SELECT customer.id FROM customer LIMIT 1";
            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );
            
            // If we can execute a simple query, connection is working
            $response->getPage()->getResponseObject();
            
            return true;
        } catch (\Exception $e) {
            Log::error('Google Ads API connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get account details
     */
    public function getAccountDetails(string $customerId): ?array
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();
            
            $query = "SELECT 
                customer.id,
                customer.descriptive_name,
                customer.currency_code,
                customer.time_zone,
                customer.status,
                customer.test_account,
                customer.manager
                FROM customer 
                WHERE customer.id = '{$customerId}'";

            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );
            
            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $customer = $googleAdsRow->getCustomer();
                
                return [
                    'customer_id' => $customer->getId(),
                    'name' => $customer->getDescriptiveName() ?: 'Unnamed Account',
                    'descriptive_name' => $customer->getDescriptiveName(),
                    'is_manager_account' => $customer->getManager(),
                    'status' => $this->mapCustomerStatus($customer->getStatus()),
                    'currency_code' => $customer->getCurrencyCode(),
                    'time_zone' => $customer->getTimeZone(),
                    'test_account' => $customer->getTestAccount(),
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to get account details', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }
}
