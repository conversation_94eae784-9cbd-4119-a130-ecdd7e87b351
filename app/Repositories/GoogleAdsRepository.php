<?php

namespace App\Repositories;

use App\Models\GoogleAdsSetting;
use Google\Ads\GoogleAds\Lib\V20\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V20\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\V20\Services\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V20\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V20\Services\CampaignServiceClient;
use Google\Ads\GoogleAds\V20\Services\CampaignBudgetServiceClient;
use Google\Ads\GoogleAds\V20\Services\AdGroupServiceClient;
use Google\Ads\GoogleAds\V20\Services\AdGroupCriterionServiceClient;
use Google\Ads\GoogleAds\V20\Services\AdGroupAdServiceClient;
use Google\Ads\GoogleAds\V20\Resources\Customer;
use Google\Ads\GoogleAds\V20\Resources\Campaign;
use Google\Ads\GoogleAds\V20\Resources\CampaignBudget;
use Google\Ads\GoogleAds\V20\Resources\AdGroup;
use Google\Ads\GoogleAds\V20\Resources\AdGroupCriterion;
use Google\Ads\GoogleAds\V20\Resources\AdGroupAd;
use Google\Ads\GoogleAds\V20\Resources\Ad;
use Google\Ads\GoogleAds\V20\Common\ResponsiveSearchAdInfo;
use Google\Ads\GoogleAds\V20\Common\AdTextAsset;
use Google\Ads\GoogleAds\V20\Common\KeywordInfo;
use Google\Ads\GoogleAds\V20\Common\ManualCpc;
use Google\Ads\GoogleAds\V20\Resources\CustomerClientLink;
use Google\Ads\GoogleAds\V20\Enums\CustomerStatusEnum\CustomerStatus;
use Google\Ads\GoogleAds\V20\Enums\CampaignStatusEnum\CampaignStatus;
use Google\Ads\GoogleAds\V20\Enums\BudgetDeliveryMethodEnum\BudgetDeliveryMethod;
use Google\Ads\GoogleAds\V20\Enums\AdvertisingChannelTypeEnum\AdvertisingChannelType;
use Google\Ads\GoogleAds\V20\Enums\BiddingStrategyTypeEnum\BiddingStrategyType;
use Google\Ads\GoogleAds\V20\Enums\AdGroupStatusEnum\AdGroupStatus;
use Google\Ads\GoogleAds\V20\Enums\AdGroupTypeEnum\AdGroupType;
use Google\Ads\GoogleAds\V20\Enums\AdGroupCriterionStatusEnum\AdGroupCriterionStatus;
use Google\Ads\GoogleAds\V20\Enums\KeywordMatchTypeEnum\KeywordMatchType;
use Google\Ads\GoogleAds\V20\Enums\AdGroupAdStatusEnum\AdGroupAdStatus;
use Google\Ads\GoogleAds\V20\Enums\ManagerLinkStatusEnum\ManagerLinkStatus;
use Google\Ads\GoogleAds\V20\Services\CreateCustomerClientRequest;
use Google\Ads\GoogleAds\V20\Services\MutateCampaignsRequest;
use Google\Ads\GoogleAds\V20\Services\MutateCustomerClientLinkRequest;
use Google\Ads\GoogleAds\V20\Services\CustomerClientLinkOperation;
use Google\Ads\GoogleAds\V20\Services\CampaignBudgetOperation;
use Google\Ads\GoogleAds\V20\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\Util\V20\ResourceNames;
use Google\Ads\GoogleAds\Util\FieldMasks;
use Google\Ads\GoogleAds\V20\Services\AdGroupAdOperation;
use Google\Ads\GoogleAds\V20\Services\AdGroupCriterionOperation;
use Google\Ads\GoogleAds\V20\Services\AdGroupOperation;
use Google\Ads\GoogleAds\V20\Services\CampaignOperation;
use Google\Ads\GoogleAds\V20\Services\MutateAdGroupAdsRequest;
use Google\Ads\GoogleAds\V20\Services\MutateAdGroupCriteriaRequest;
use Google\Ads\GoogleAds\V20\Services\MutateAdGroupsRequest;
use Google\Ads\GoogleAds\V20\Services\MutateCampaignBudgetsRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GoogleAdsRepository
{
    protected GoogleAdsClient $client;

    public function __construct()
    {
        $this->initializeClient();
    }

    /**
     * Initialize Google Ads client
     */
    protected function initializeClient(): void
    {
        try {
            $oAuth2Credential = (new OAuth2TokenBuilder())
                ->withClientId(GoogleAdsSetting::getValue('oauth2_client_id'))
                ->withClientSecret(GoogleAdsSetting::getValue('oauth2_client_secret'))
                ->withRefreshToken(GoogleAdsSetting::getValue('oauth2_refresh_token'))
                ->build();

            $this->client = (new GoogleAdsClientBuilder())
                ->withOAuth2Credential($oAuth2Credential)
                ->withDeveloperToken(GoogleAdsSetting::getValue('developer_token'))
                ->withLoginCustomerId(GoogleAdsSetting::getValue('login_customer_id'))
                ->build();
        } catch (\Exception $e) {
            Log::error('Failed to initialize Google Ads client', ['error' => $e->getMessage()]);
            throw new \Exception('Google Ads client initialization failed: ' . $e->getMessage());
        }
    }

    /**
     * Get child accounts using GAQL
     */
    public function getChildAccounts(string $managerCustomerId): array
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();

            $query = "SELECT 
                customer_client.client_customer,
                customer_client.level,
                customer_client.manager,
                customer_client.descriptive_name,
                customer_client.currency_code,
                customer_client.time_zone,
                customer_client.id,
                customer_client.status,
                customer_client.test_account
                FROM customer_client 
                WHERE customer_client.level <= 1";

            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($managerCustomerId, $query)
            );

            $accounts = [];
            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $customerClient = $googleAdsRow->getCustomerClient();

                $accounts[] = [
                    'customer_id' => $customerClient->getId(),
                    'name' => $customerClient->getDescriptiveName() ?: 'Unnamed Account',
                    'descriptive_name' => $customerClient->getDescriptiveName(),
                    'is_manager_account' => $customerClient->getManager(),
                    'status' => $this->mapCustomerStatus($customerClient->getStatus()),
                    'currency_code' => $customerClient->getCurrencyCode(),
                    'time_zone' => $customerClient->getTimeZone(),
                    'manager_customer_id' => $customerClient->getManager() ? null : $managerCustomerId,
                    'can_manage_clients' => $customerClient->getManager(),
                    'test_account' => $customerClient->getTestAccount(),
                    'raw_data' => [
                        'level' => $customerClient->getLevel(),
                        'client_customer' => $customerClient->getClientCustomer(),
                    ]
                ];
            }

            return $accounts;
        } catch (\Exception $e) {
            Log::error('Failed to get child accounts', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId
            ]);
            throw $e;
        }
    }

    /**
     * Create a new customer account
     */
    public function createAccount(array $data): array
    {
        try {
            $customerService = $this->client->getCustomerServiceClient();

            $customer = new Customer([
                'descriptive_name' => $data['name'],
                'currency_code' => $data['currency_code'] ?? 'USD',
                'time_zone' => $data['time_zone'] ?? 'America/New_York',
            ]);

            $response = $customerService->createCustomerClient(
                CreateCustomerClientRequest::build(
                    GoogleAdsSetting::getValue('login_customer_id'),
                    $customer
                )
            );

            // Get the created customer ID from the response
            $customerId = $response->getResourceName();
            $customerId = substr($customerId, strrpos($customerId, '/') + 1);

            return [
                'customer_id' => $customerId,
                'name' => $data['name'],
                'descriptive_name' => $data['name'],
                'is_manager_account' => $data['is_manager_account'] ?? false,
                'status' => 'ENABLED',
                'currency_code' => $data['currency_code'] ?? 'USD',
                'time_zone' => $data['time_zone'] ?? 'America/New_York',
                'manager_customer_id' => GoogleAdsSetting::getValue('login_customer_id'),
                'can_manage_clients' => $data['is_manager_account'] ?? false,
                'test_account' => $data['test_account'] ?? false,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create customer account', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Pause all campaigns in an account
     */
    public function pauseAllCampaigns(string $customerId): bool
    {
        try {
            return $this->updateAllCampaignsStatus($customerId, CampaignStatus::PAUSED);
        } catch (\Exception $e) {
            Log::error('Failed to pause campaigns', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }

    /**
     * Enable all campaigns in an account
     */
    public function enableAllCampaigns(string $customerId): bool
    {
        try {
            return $this->updateAllCampaignsStatus($customerId, CampaignStatus::ENABLED);
        } catch (\Exception $e) {
            Log::error('Failed to enable campaigns', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }

    /**
     * Update all campaigns status
     */
    protected function updateAllCampaignsStatus(string $customerId, int $status): bool
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();

            // First, get all campaigns
            $query = "SELECT campaign.id, campaign.name FROM campaign WHERE campaign.status != 'REMOVED'";
            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );

            $campaignService = $this->client->getCampaignServiceClient();
            $operations = [];

            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $campaign = $googleAdsRow->getCampaign();

                $campaignUpdate = new Campaign([
                    'resource_name' => $campaign->getResourceName(),
                    'status' => $status
                ]);

                $operations[] = $campaignService->newCampaignOperation()
                    ->setUpdate($campaignUpdate)
                    ->setUpdateMask(['status']);
            }

            if (!empty($operations)) {
                $campaignService->mutateCampaigns(
                    MutateCampaignsRequest::build($customerId, $operations)
                );
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update campaigns status', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'status' => $status
            ]);
            throw $e;
        }
    }

    /**
     * Map Google Ads customer status to our status
     */
    protected function mapCustomerStatus(?int $status): string
    {
        return match ($status) {
            CustomerStatus::ENABLED => 'ENABLED',
            CustomerStatus::CANCELED => 'REMOVED',
            CustomerStatus::SUSPENDED => 'PAUSED',
            default => 'ENABLED',
        };
    }

    /**
     * Test API connection
     */
    public function testConnection(): bool
    {
        try {
            $customerService = $this->client->getCustomerServiceClient();
            $customerId = GoogleAdsSetting::getValue('login_customer_id');

            $googleAdsService = $this->client->getGoogleAdsServiceClient();
            $query = "SELECT customer.id FROM customer LIMIT 1";
            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );

            // If we can execute a simple query, connection is working
            $response->getPage()->getResponseObject();

            return true;
        } catch (\Exception $e) {
            Log::error('Google Ads API connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get account details
     */
    public function getAccountDetails(string $customerId): ?array
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();

            $query = "SELECT 
                customer.id,
                customer.descriptive_name,
                customer.currency_code,
                customer.time_zone,
                customer.status,
                customer.test_account,
                customer.manager
                FROM customer 
                WHERE customer.id = '{$customerId}'";

            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($customerId, $query)
            );

            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $customer = $googleAdsRow->getCustomer();

                return [
                    'customer_id' => $customer->getId(),
                    'name' => $customer->getDescriptiveName() ?: 'Unnamed Account',
                    'descriptive_name' => $customer->getDescriptiveName(),
                    'is_manager_account' => $customer->getManager(),
                    'status' => $this->mapCustomerStatus($customer->getStatus()),
                    'currency_code' => $customer->getCurrencyCode(),
                    'time_zone' => $customer->getTimeZone(),
                    'test_account' => $customer->getTestAccount(),
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to get account details', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            throw $e;
        }
    }

    /**
     * Link a client account to a manager account
     */
    public function linkClientToManager(string $managerCustomerId, string $clientCustomerId): bool
    {
        try {
            $customerClientLinkService = $this->client->getCustomerClientLinkServiceClient();

            // Create a customer client link
            $customerClientLink = new CustomerClientLink([
                'client_customer' => ResourceNames::forCustomer($clientCustomerId),
                'status' => ManagerLinkStatus::ACTIVE
            ]);

            // Create a customer client link operation
            $customerClientLinkOperation = new CustomerClientLinkOperation();
            $customerClientLinkOperation->setCreate($customerClientLink);

            // Issue a mutate request to create the customer client link
            $response = $customerClientLinkService->mutateCustomerClientLink(
                MutateCustomerClientLinkRequest::build(
                    $managerCustomerId,
                    $customerClientLinkOperation
                )
            );

            Log::info('Successfully linked client to manager', [
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId,
                'resource_name' => $response->getResult()->getResourceName()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to link client to manager', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId
            ]);
            throw $e;
        }
    }

    /**
     * Unlink a client account from a manager account
     */
    public function unlinkClientFromManager(string $managerCustomerId, string $clientCustomerId): bool
    {
        try {
            // First, find the existing customer client link
            $customerClientLinkResourceName = $this->findCustomerClientLink($managerCustomerId, $clientCustomerId);

            if (!$customerClientLinkResourceName) {
                throw new \Exception("No existing link found between manager {$managerCustomerId} and client {$clientCustomerId}");
            }

            $customerClientLinkService = $this->client->getCustomerClientLinkServiceClient();

            // Create a customer client link with REMOVED status
            $customerClientLink = new CustomerClientLink([
                'resource_name' => $customerClientLinkResourceName,
                'status' => ManagerLinkStatus::INACTIVE
            ]);

            // Create a customer client link operation for updating
            $customerClientLinkOperation = new CustomerClientLinkOperation();
            $customerClientLinkOperation->setUpdate($customerClientLink);
            $customerClientLinkOperation->setUpdateMask(
                FieldMasks::allSetFieldsOf($customerClientLink)
            );

            // Issue a mutate request to update the customer client link
            $response = $customerClientLinkService->mutateCustomerClientLink(
                MutateCustomerClientLinkRequest::build(
                    $managerCustomerId,
                    $customerClientLinkOperation
                )
            );

            Log::info('Successfully unlinked client from manager', [
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId,
                'resource_name' => $customerClientLinkResourceName
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to unlink client from manager', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId
            ]);
            throw $e;
        }
    }

    /**
     * Find existing customer client link resource name
     */
    protected function findCustomerClientLink(string $managerCustomerId, string $clientCustomerId): ?string
    {
        try {
            $googleAdsService = $this->client->getGoogleAdsServiceClient();

            $query = "SELECT customer_client_link.resource_name 
                     FROM customer_client_link 
                     WHERE customer_client_link.client_customer = 'customers/{$clientCustomerId}'";

            $response = $googleAdsService->search(
                SearchGoogleAdsRequest::build($managerCustomerId, $query)
            );

            foreach ($response->iterateAllElements() as $googleAdsRow) {
                $customerClientLink = $googleAdsRow->getCustomerClientLink();
                return $customerClientLink->getResourceName();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to find customer client link', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId
            ]);
            throw $e;
        }
    }

    /**
     * Check if a client is linked to a manager
     */
    public function isClientLinkedToManager(string $managerCustomerId, string $clientCustomerId): bool
    {
        try {
            $linkResourceName = $this->findCustomerClientLink($managerCustomerId, $clientCustomerId);
            return $linkResourceName !== null;
        } catch (\Exception $e) {
            Log::error('Failed to check client link status', [
                'error' => $e->getMessage(),
                'manager_customer_id' => $managerCustomerId,
                'client_customer_id' => $clientCustomerId
            ]);
            return false;
        }
    }

    /**
     * Create a complete Google Ads Search campaign
     */
    public function createSearchCampaign(array $data): array
    {
        try {
            $customerId = $data['customer_id'];

            // Step 1: Create campaign budget
            $budgetResourceName = $this->createCampaignBudget($customerId, $data);

            // Step 2: Create campaign
            $campaignResourceName = $this->createCampaign($customerId, $data, $budgetResourceName);

            // Step 3: Create ad group
            $adGroupResourceName = $this->createAdGroup($customerId, $data, $campaignResourceName);

            // Step 4: Add keywords
            $this->addKeywords($customerId, $data, $adGroupResourceName);

            // Step 5: Create responsive search ad
            $adResourceName = $this->createResponsiveSearchAd($customerId, $data, $adGroupResourceName);

            // Extract IDs from resource names
            $campaignId = $this->extractIdFromResourceName($campaignResourceName);
            $adGroupId = $this->extractIdFromResourceName($adGroupResourceName);

            return [
                'campaign_id' => $campaignId,
                'campaign_resource_name' => $campaignResourceName,
                'ad_group_id' => $adGroupId,
                'ad_group_resource_name' => $adGroupResourceName,
                'ad_resource_name' => $adResourceName,
                'budget_resource_name' => $budgetResourceName,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create search campaign', [
                'error' => $e->getMessage(),
                'customer_id' => $data['customer_id'] ?? 'unknown'
            ]);
            throw $e;
        }
    }

    /**
     * Create campaign budget
     */
    protected function createCampaignBudget(string $customerId, array $data): string
    {
        $campaignBudgetService = $this->client->getCampaignBudgetServiceClient();

        $campaignBudget = new CampaignBudget([
            'name' => $data['campaign_name'] . ' Budget',
            'amount_micros' => $data['daily_budget_micros'],
            'delivery_method' => BudgetDeliveryMethod::STANDARD,
        ]);
        $operation = new CampaignBudgetOperation();
        $operation->setCreate($campaignBudget);

        $response = $campaignBudgetService->mutateCampaignBudgets(
            MutateCampaignBudgetsRequest::build($customerId, [$operation])
        );

        return $response->getResults()[0]->getResourceName();
    }

    /**
     * Create campaign
     */
    protected function createCampaign(string $customerId, array $data, string $budgetResourceName): string
    {
        $campaignService = $this->client->getCampaignServiceClient();

        $campaign = new Campaign([
            'name' => $data['campaign_name'],
            'advertising_channel_type' => AdvertisingChannelType::SEARCH,
            'status' => CampaignStatus::PAUSED, // Start paused for safety
            'campaign_budget' => $budgetResourceName,
            'bidding_strategy_type' => BiddingStrategyType::MANUAL_CPC,
            'manual_cpc' => new ManualCpc([
                'enhanced_cpc_enabled' => true
            ]),
        ]);

        $operation = new CampaignOperation();
        $operation->setCreate($campaign);

        $response = $campaignService->mutateCampaigns(
            MutateCampaignsRequest::build($customerId, [$operation])
        );

        return $response->getResults()[0]->getResourceName();
    }

    /**
     * Create ad group
     */
    protected function createAdGroup(string $customerId, array $data, string $campaignResourceName): string
    {
        $adGroupService = $this->client->getAdGroupServiceClient();

        $adGroup = new AdGroup([
            'name' => $data['ad_group_name'],
            'campaign' => $campaignResourceName,
            'status' => AdGroupStatus::ENABLED,
            'type' => AdGroupType::SEARCH_STANDARD,
            'cpc_bid_micros' => $data['cpc_bid_micros'],
        ]);

        $operation = new AdGroupOperation();
        $operation->setCreate($adGroup);

        $response = $adGroupService->mutateAdGroups(
            MutateAdGroupsRequest::build($customerId, [$operation])
        );

        return $response->getResults()[0]->getResourceName();
    }

    /**
     * Add keywords to ad group
     */
    protected function addKeywords(string $customerId, array $data, string $adGroupResourceName): void
    {
        $adGroupCriterionService = $this->client->getAdGroupCriterionServiceClient();
        $operations = [];

        foreach ($data['keywords'] as $keyword) {
            $keywordInfo = new KeywordInfo([
                'text' => $keyword['text'],
                'match_type' => $this->mapKeywordMatchType($keyword['match_type'])
            ]);

            $adGroupCriterion = new AdGroupCriterion([
                'ad_group' => $adGroupResourceName,
                'status' => AdGroupCriterionStatus::ENABLED,
                'keyword' => $keywordInfo,
            ]);

            $operation = new AdGroupCriterionOperation();
            $operation->setCreate($adGroupCriterion);

            $operations[] = $operation;
        }

        if (!empty($operations)) {
            $adGroupCriterionService->mutateAdGroupCriteria(
                MutateAdGroupCriteriaRequest::build($customerId, $operations)
            );
        }
    }

    /**
     * Create responsive search ad
     */
    protected function createResponsiveSearchAd(string $customerId, array $data, string $adGroupResourceName): string
    {
        $adGroupAdService = $this->client->getAdGroupAdServiceClient();

        $responsiveSearchAdInfo = new ResponsiveSearchAdInfo([
            'headlines' => [
                new AdTextAsset(['text' => $data['headline_1']]),
                new AdTextAsset(['text' => $data['headline_2']]),
            ],
            'descriptions' => [
                new AdTextAsset(['text' => $data['description']]),
            ],
        ]);

        $ad = new Ad([
            'responsive_search_ad' => $responsiveSearchAdInfo,
            'final_urls' => [$data['final_url']],
        ]);

        $adGroupAd = new AdGroupAd([
            'ad_group' => $adGroupResourceName,
            'status' => AdGroupAdStatus::ENABLED,
            'ad' => $ad,
        ]);

        $operation = new AdGroupAdOperation();
        $operation->setCreate($adGroupAd);

        $response = $adGroupAdService->mutateAdGroupAds(
            MutateAdGroupAdsRequest::build($customerId, [$operation])
        );

        return $response->getResults()[0]->getResourceName();
    }

    /**
     * Map keyword match type to Google Ads enum
     */
    protected function mapKeywordMatchType(string $matchType): int
    {
        return match ($matchType) {
            'EXACT' => KeywordMatchType::EXACT,
            'PHRASE' => KeywordMatchType::PHRASE,
            'BROAD_MODIFIED' => KeywordMatchType::BROAD,
            default => KeywordMatchType::BROAD,
        };
    }

    /**
     * Extract ID from Google Ads resource name
     */
    protected function extractIdFromResourceName(string $resourceName): string
    {
        return substr($resourceName, strrpos($resourceName, '/') + 1);
    }
}
