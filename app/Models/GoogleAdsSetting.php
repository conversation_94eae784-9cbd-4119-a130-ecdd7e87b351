<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Crypt;

class GoogleAdsSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_encrypted',
        'is_required',
        'group',
    ];

    protected $casts = [
        'is_encrypted' => 'boolean',
        'is_required' => 'boolean',
    ];

    /**
     * Get the decrypted value if encrypted
     */
    public function getDecryptedValueAttribute(): mixed
    {
        if ($this->is_encrypted && $this->value) {
            try {
                return Crypt::decryptString($this->value);
            } catch (\Exception $e) {
                return null;
            }
        }

        return $this->castValue($this->value);
    }

    /**
     * Set encrypted value if needed
     */
    public function setValueAttribute($value): void
    {
        if ($this->is_encrypted && $value !== null) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    /**
     * Cast value to appropriate type
     */
    protected function castValue($value): mixed
    {
        if ($value === null) {
            return null;
        }

        return match ($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json' => json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Get setting value by key
     */
    public static function getValue(string $key, mixed $default = null): mixed
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->decrypted_value : $default;
    }

    /**
     * Set setting value by key
     */
    public static function setValue(string $key, mixed $value, array $attributes = []): static
    {
        return static::updateOrCreate(
            ['key' => $key],
            array_merge($attributes, ['value' => $value])
        );
    }

    /**
     * Get all settings grouped by group
     */
    public static function getGroupedSettings(): array
    {
        return static::all()
            ->groupBy('group')
            ->map(fn ($settings) => $settings->keyBy('key'))
            ->toArray();
    }

    /**
     * Check if all required settings are configured
     */
    public static function areRequiredSettingsConfigured(): bool
    {
        $requiredSettings = static::where('is_required', true)->get();

        foreach ($requiredSettings as $setting) {
            if (empty($setting->decrypted_value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get missing required settings
     */
    public static function getMissingRequiredSettings(): array
    {
        return static::where('is_required', true)
            ->get()
            ->filter(fn ($setting) => empty($setting->decrypted_value))
            ->pluck('key')
            ->toArray();
    }
}
