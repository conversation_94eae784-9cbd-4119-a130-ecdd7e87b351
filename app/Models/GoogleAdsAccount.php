<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GoogleAdsAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'name',
        'descriptive_name',
        'is_manager_account',
        'status',
        'currency_code',
        'time_zone',
        'manager_customer_id',
        'can_manage_clients',
        'test_account',
        'optimization_score',
        'last_synced_at',
        'raw_data',
    ];

    protected $casts = [
        'is_manager_account' => 'boolean',
        'can_manage_clients' => 'boolean',
        'test_account' => 'boolean',
        'optimization_score' => 'array',
        'raw_data' => 'array',
        'last_synced_at' => 'datetime',
    ];

    /**
     * Get the manager account (parent MCC)
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(GoogleAdsAccount::class, 'manager_customer_id', 'customer_id');
    }

    /**
     * Get child accounts (if this is an MCC)
     */
    public function childAccounts(): HasMany
    {
        return $this->hasMany(GoogleAdsAccount::class, 'manager_customer_id', 'customer_id');
    }

    /**
     * Get all descendant accounts recursively
     */
    public function descendants(): HasMany
    {
        return $this->childAccounts()->with('descendants');
    }

    /**
     * Check if account is enabled
     */
    public function isEnabled(): bool
    {
        return $this->status === 'ENABLED';
    }

    /**
     * Check if account is an MCC
     */
    public function isMcc(): bool
    {
        return $this->is_manager_account;
    }

    /**
     * Get formatted customer ID
     */
    protected function formattedCustomerId(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->formatCustomerId($this->customer_id)
        );
    }

    /**
     * Format customer ID with dashes (123-456-7890)
     */
    public static function formatCustomerId(string $customerId): string
    {
        $cleaned = preg_replace('/[^0-9]/', '', $customerId);
        if (strlen($cleaned) === 10) {
            return substr($cleaned, 0, 3) . '-' . substr($cleaned, 3, 3) . '-' . substr($cleaned, 6);
        }
        return $customerId;
    }

    /**
     * Get status badge color for Filament
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            'ENABLED' => 'success',
            'PAUSED' => 'warning',
            'REMOVED' => 'danger',
            default => 'gray',
        };
    }

    /**
     * Scope to get only MCC accounts
     */
    public function scopeMccAccounts($query)
    {
        return $query->where('is_manager_account', true);
    }

    /**
     * Scope to get only regular accounts
     */
    public function scopeRegularAccounts($query)
    {
        return $query->where('is_manager_account', false);
    }

    /**
     * Scope to get enabled accounts
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 'ENABLED');
    }
}
