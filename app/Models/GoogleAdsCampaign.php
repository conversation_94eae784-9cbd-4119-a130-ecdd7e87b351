<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;

class GoogleAdsCampaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'campaign_id',
        'campaign_name',
        'ad_group_name',
        'ad_group_id',
        'daily_budget_micros',
        'cpc_bid_micros',
        'keywords',
        'headline_1',
        'headline_2',
        'description',
        'final_url',
        'status',
        'error_message',
        'api_response',
        'user_id',
        'created_at_api',
    ];

    protected $casts = [
        'keywords' => 'array',
        'api_response' => 'array',
        'daily_budget_micros' => 'decimal:0',
        'cpc_bid_micros' => 'decimal:0',
        'created_at_api' => 'datetime',
    ];

    /**
     * Get the Google Ads account this campaign belongs to
     */
    public function googleAdsAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdsAccount::class, 'customer_id', 'customer_id');
    }

    /**
     * Get the user who created this campaign
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get daily budget in dollars
     */
    protected function dailyBudgetDollars(): Attribute
    {
        return Attribute::make(
            get: fn () => (float) ($this->daily_budget_micros / 1000000),
            set: fn ($value) => ['daily_budget_micros' => $value * 1000000]
        );
    }

    /**
     * Get CPC bid in dollars
     */
    protected function cpcBidDollars(): Attribute
    {
        return Attribute::make(
            get: fn () => (float) ($this->cpc_bid_micros / 1000000),
            set: fn ($value) => ['cpc_bid_micros' => $value * 1000000]
        );
    }

    /**
     * Get status badge color for Filament
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            'CREATED' => 'success',
            'PENDING' => 'warning',
            'FAILED' => 'danger',
            default => 'gray',
        };
    }

    /**
     * Check if campaign was successfully created
     */
    public function isCreated(): bool
    {
        return $this->status === 'CREATED' && !empty($this->campaign_id);
    }

    /**
     * Check if campaign creation failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'FAILED';
    }

    /**
     * Check if campaign creation is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'PENDING';
    }

    /**
     * Get formatted keywords for display
     */
    public function getFormattedKeywordsAttribute(): string
    {
        if (empty($this->keywords)) {
            return '';
        }

        return collect($this->keywords)->map(function ($keyword) {
            $text = $keyword['text'] ?? $keyword;
            $matchType = $keyword['match_type'] ?? 'BROAD';

            return match ($matchType) {
                'EXACT' => "[{$text}]",
                'PHRASE' => "\"{$text}\"",
                'BROAD_MODIFIED' => "+{$text}",
                default => $text,
            };
        })->join(', ');
    }

    /**
     * Scope to get campaigns by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get successful campaigns
     */
    public function scopeCreated($query)
    {
        return $query->where('status', 'CREATED');
    }

    /**
     * Scope to get failed campaigns
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'FAILED');
    }

    /**
     * Scope to get pending campaigns
     */
    public function scopePending($query)
    {
        return $query->where('status', 'PENDING');
    }
}
