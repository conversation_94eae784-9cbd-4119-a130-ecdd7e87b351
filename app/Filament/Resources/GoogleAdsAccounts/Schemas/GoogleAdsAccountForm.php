<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class GoogleAdsAccountForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Account Information')
                    ->columnSpanFull()
                    ->columns()
                    ->schema([
                        TextInput::make('name')
                            ->label('Account Name')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('customer_id')
                            ->label('Customer ID')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->placeholder('123-456-7890')
                            ->helperText('Google Ads Customer ID (10 digits)'),
                        Select::make('currency_code')
                            ->label('Currency')
                            ->options([
                                'USD' => 'USD - US Dollar',
                                'EUR' => 'EUR - Euro',
                                'GBP' => 'GBP - British Pound',
                                'CAD' => 'CAD - Canadian Dollar',
                                'AUD' => 'AUD - Australian Dollar',
                                'JPY' => 'JPY - Japanese Yen',
                            ])
                            ->default('USD')
                            ->searchable(),
                        Select::make('time_zone')
                            ->label('Time Zone')
                            ->options([
                                'America/New_York' => 'Eastern Time',
                                'America/Chicago' => 'Central Time',
                                'America/Denver' => 'Mountain Time',
                                'America/Los_Angeles' => 'Pacific Time',
                                'Europe/London' => 'London',
                                'Europe/Paris' => 'Paris',
                                'Asia/Tokyo' => 'Tokyo',
                            ])
                            ->default('America/New_York')
                            ->searchable(),
                        Select::make('status')
                            ->label('Status')
                            ->options([
                                'ENABLED' => 'Enabled',
                                'PAUSED' => 'Paused',
                                'REMOVED' => 'Removed',
                            ])
                            ->default('ENABLED')
                            ->required(),
                        Toggle::make('is_manager_account')
                            ->label('MCC Account')
                            ->helperText('Is this a Manager Customer Center account?'),
                        Toggle::make('test_account')
                            ->label('Test Account')
                            ->helperText('Is this a test account?'),
                        Toggle::make('can_manage_clients')
                            ->label('Can Manage Clients')
                            ->helperText('Can this account manage other accounts?'),

                        TextInput::make('manager_customer_id')
                            ->label('Manager Customer ID')
                            ->placeholder('123-456-7890')
                            ->helperText('Parent MCC Customer ID (if applicable)'),

                        TextInput::make('descriptive_name')
                            ->label('Descriptive Name')
                            ->maxLength(255)
                            ->helperText('Additional descriptive name for the account'),
                        DateTimePicker::make('last_synced_at')
                            ->label('Last Synced')
                            ->disabled()
                            ->helperText('Last time this account was synced with Google Ads API'),
                    ]),
            ]);
    }
}
