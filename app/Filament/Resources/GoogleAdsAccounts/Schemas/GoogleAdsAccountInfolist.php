<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class GoogleAdsAccountInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('customer_id'),
                TextEntry::make('name'),
                TextEntry::make('descriptive_name'),
                IconEntry::make('is_manager_account')
                    ->boolean(),
                TextEntry::make('status'),
                TextEntry::make('currency_code'),
                TextEntry::make('time_zone'),
                TextEntry::make('manager_customer_id'),
                IconEntry::make('can_manage_clients')
                    ->boolean(),
                IconEntry::make('test_account')
                    ->boolean(),
                TextEntry::make('last_synced_at')
                    ->dateTime(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
