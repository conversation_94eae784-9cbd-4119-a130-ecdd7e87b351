<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Tables;

use App\Models\GoogleAdsAccount;
use App\Services\GoogleAdsService;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;

class GoogleAdsAccountsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('customer_id')
                    ->label('Customer ID')
                    ->formatStateUsing(fn(string $state): string => GoogleAdsAccount::formatCustomerId($state))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Account Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'ENABLED', 'LINKED' => 'success',
                        'PAUSED', 'UNLINKED' => 'warning',
                        'REMOVED' => 'danger',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),
                IconColumn::make('is_manager_account')
                    ->label('MCC')
                    ->boolean()
                    ->trueIcon('heroicon-o-building-office')
                    ->falseIcon('heroicon-o-user')
                    ->sortable(),
                TextColumn::make('currency_code')
                    ->label('Currency')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('manager.name')
                    ->label('Manager Account')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('Root Account'),
                IconColumn::make('test_account')
                    ->label('Test')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('last_synced_at')
                    ->label('Last Synced')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'ENABLED' => 'Enabled',
                        'PAUSED' => 'Paused',
                        'REMOVED' => 'Removed',
                        'LINKED' => 'Linked to Manager',
                        'UNLINKED' => 'Unlinked from Manager',
                    ]),
                TernaryFilter::make('is_manager_account')
                    ->label('Account Type')
                    ->placeholder('All accounts')
                    ->trueLabel('MCC accounts only')
                    ->falseLabel('Regular accounts only'),
                TernaryFilter::make('test_account')
                    ->label('Test Account')
                    ->placeholder('All accounts')
                    ->trueLabel('Test accounts only')
                    ->falseLabel('Live accounts only'),
            ])
            ->recordActions([
                ActionGroup::make([

                    ViewAction::make(),
                    EditAction::make(),
                    Action::make('sync')
                        ->icon('heroicon-o-arrow-path')
                        ->color('info')
                        ->action(function (GoogleAdsAccount $record) {
                            try {
                                $service = app(GoogleAdsService::class);
                                $service->syncChildAccounts($record->customer_id);

                                Notification::make()
                                    ->title('Account synced successfully')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Sync failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->requiresConfirmation(),
                    Action::make('disable')
                        ->icon('heroicon-o-link-slash')
                        ->color('warning')
                        ->label('Unlink from Manager')
                        ->action(function (GoogleAdsAccount $record) {
                            try {
                                $service = app(GoogleAdsService::class);
                                $service->disableAccount($record);

                                Notification::make()
                                    ->title('Account unlinked successfully')
                                    ->body('Account has been unlinked from the manager account')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Unlink failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Unlink Account from Manager')
                        ->modalDescription('This will unlink the account from the manager account. The account will no longer be managed by this MCC.')
                        ->modalSubmitActionLabel('Unlink Account')
                        ->visible(
                            fn(GoogleAdsAccount $record): bool =>
                            $record->status === 'ENABLED' &&
                                !$record->is_manager_account &&
                                $record->manager_customer_id
                        ),
                    Action::make('enable')
                        ->icon('heroicon-o-link')
                        ->color('success')
                        ->label('Link to Manager')
                        ->action(function (GoogleAdsAccount $record) {
                            try {
                                $service = app(GoogleAdsService::class);
                                $service->enableAccount($record);

                                Notification::make()
                                    ->title('Account linked successfully')
                                    ->body('Account has been linked to the manager account')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Link failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Link Account to Manager')
                        ->modalDescription('This will link the account to the manager account. The account will be managed by this MCC.')
                        ->modalSubmitActionLabel('Link Account')
                        ->visible(
                            fn(GoogleAdsAccount $record): bool =>
                            $record->status === 'PAUSED' &&
                                !$record->is_manager_account
                        ),
                    Action::make('check_link_status')
                        ->icon('heroicon-o-information-circle')
                        ->color('info')
                        ->label('Check Link Status')
                        ->action(function (GoogleAdsAccount $record) {
                            try {
                                $service = app(GoogleAdsService::class);
                                $isLinked = $service->isAccountLinked($record);

                                Notification::make()
                                    ->title('Link Status')
                                    ->body($isLinked ? 'Account is linked to manager' : 'Account is not linked to manager')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Check failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->visible(fn(GoogleAdsAccount $record): bool => !$record->is_manager_account),
                    Action::make('invite_user')
                        ->icon('heroicon-o-user-plus')
                        ->color('info')
                        ->schema([
                            TextInput::make('email')
                                ->email()
                                ->required()
                                ->label('Email Address'),
                            Select::make('role')
                                ->options([
                                    'ADMIN' => 'Admin',
                                    'STANDARD' => 'Standard',
                                    'READ_ONLY' => 'Read Only',
                                ])
                                ->default('STANDARD')
                                ->required(),
                        ])
                        ->action(function (GoogleAdsAccount $record, array $data) {
                            try {
                                $service = app(GoogleAdsService::class);
                                $service->inviteUser($record, $data['email'], $data['role']);

                                Notification::make()
                                    ->title('User invitation sent')
                                    ->body("Invitation sent to {$data['email']}")
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Invitation failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),
                ])
            ])
            ->toolbarActions([
                Action::make('sync_all')
                    ->label('Sync All Accounts')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function () {
                        try {
                            $service = app(GoogleAdsService::class);
                            $accounts = $service->syncChildAccounts();

                            Notification::make()
                                ->title('Accounts synced successfully')
                                ->body("Synced {$accounts->count()} accounts")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Sync failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
