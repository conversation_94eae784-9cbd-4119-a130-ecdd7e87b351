<?php

namespace App\Filament\Resources\GoogleAdsAccounts;

use App\Filament\Resources\GoogleAdsAccounts\Pages\CreateGoogleAdsAccount;
use App\Filament\Resources\GoogleAdsAccounts\Pages\EditGoogleAdsAccount;
use App\Filament\Resources\GoogleAdsAccounts\Pages\ListGoogleAdsAccounts;
use App\Filament\Resources\GoogleAdsAccounts\Pages\ViewGoogleAdsAccount;
use App\Filament\Resources\GoogleAdsAccounts\Schemas\GoogleAdsAccountForm;
use App\Filament\Resources\GoogleAdsAccounts\Schemas\GoogleAdsAccountInfolist;
use App\Filament\Resources\GoogleAdsAccounts\Tables\GoogleAdsAccountsTable;
use App\Models\GoogleAdsAccount;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class GoogleAdsAccountResource extends Resource
{
    protected static ?string $model = GoogleAdsAccount::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function form(Schema $schema): Schema
    {
        return GoogleAdsAccountForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return GoogleAdsAccountInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return GoogleAdsAccountsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListGoogleAdsAccounts::route('/'),
            'create' => CreateGoogleAdsAccount::route('/create'),
            'view' => ViewGoogleAdsAccount::route('/{record}'),
            'edit' => EditGoogleAdsAccount::route('/{record}/edit'),
        ];
    }
}
