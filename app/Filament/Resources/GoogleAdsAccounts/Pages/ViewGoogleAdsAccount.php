<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Pages;

use App\Filament\Resources\GoogleAdsAccounts\GoogleAdsAccountResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewGoogleAdsAccount extends ViewRecord
{
    protected static string $resource = GoogleAdsAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
