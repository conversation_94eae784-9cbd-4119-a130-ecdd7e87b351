<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Pages;

use App\Filament\Resources\GoogleAdsAccounts\GoogleAdsAccountResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditGoogleAdsAccount extends EditRecord
{
    protected static string $resource = GoogleAdsAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
