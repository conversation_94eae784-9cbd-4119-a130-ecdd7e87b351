<?php

namespace App\Filament\Resources\GoogleAdsAccounts\Pages;

use App\Filament\Resources\GoogleAdsAccounts\GoogleAdsAccountResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListGoogleAdsAccounts extends ListRecords
{
    protected static string $resource = GoogleAdsAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
