<?php

namespace App\Filament\Resources\AuditLogs\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class AuditLogForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('user_id')
                    ->relationship('user', 'name'),
                TextInput::make('action')
                    ->required(),
                TextInput::make('model_type')
                    ->required(),
                TextInput::make('model_id'),
                TextInput::make('customer_id'),
                Textarea::make('description')
                    ->required()
                    ->columnSpanFull(),
                Textarea::make('old_values')
                    ->columnSpanFull(),
                Textarea::make('new_values')
                    ->columnSpanFull(),
                Textarea::make('metadata')
                    ->columnSpanFull(),
                TextInput::make('ip_address'),
                TextInput::make('user_agent'),
                TextInput::make('status')
                    ->required()
                    ->default('success'),
                Textarea::make('error_message')
                    ->columnSpanFull(),
            ]);
    }
}
