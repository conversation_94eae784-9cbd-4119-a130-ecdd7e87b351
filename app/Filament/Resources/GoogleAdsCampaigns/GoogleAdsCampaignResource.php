<?php

namespace App\Filament\Resources\GoogleAdsCampaigns;

use App\Filament\Resources\GoogleAdsCampaigns\Pages\CreateGoogleAdsCampaign;
use App\Filament\Resources\GoogleAdsCampaigns\Pages\EditGoogleAdsCampaign;
use App\Filament\Resources\GoogleAdsCampaigns\Pages\ListGoogleAdsCampaigns;
use App\Filament\Resources\GoogleAdsCampaigns\Schemas\GoogleAdsCampaignForm;
use App\Filament\Resources\GoogleAdsCampaigns\Tables\GoogleAdsCampaignsTable;
use App\Models\GoogleAdsCampaign;
use BackedEnum;
use UnitEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class GoogleAdsCampaignResource extends Resource
{
    protected static ?string $model = GoogleAdsCampaign::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedMegaphone;

    protected static string|UnitEnum|null $navigationGroup = 'Google Ads';

    protected static ?string $navigationLabel = 'Campaign Creation';

    protected static ?int $navigationSort = 2;

    public static function form(Schema $schema): Schema
    {
        return GoogleAdsCampaignForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return GoogleAdsCampaignsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListGoogleAdsCampaigns::route('/'),
            'create' => CreateGoogleAdsCampaign::route('/create'),
            'edit' => EditGoogleAdsCampaign::route('/{record}/edit'),
        ];
    }
}
