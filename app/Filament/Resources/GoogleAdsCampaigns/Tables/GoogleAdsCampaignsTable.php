<?php

namespace App\Filament\Resources\GoogleAdsCampaigns\Tables;

use App\Models\GoogleAdsCampaign;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class GoogleAdsCampaignsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('campaign_name')
                    ->label('Campaign Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('googleAdsAccount.name')
                    ->label('Account')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'CREATED' => 'success',
                        'PENDING' => 'warning',
                        'FAILED' => 'danger',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),

                TextColumn::make('daily_budget_dollars')
                    ->label('Daily Budget')
                    ->money('USD')
                    ->sortable(),

                TextColumn::make('cpc_bid_dollars')
                    ->label('Max CPC')
                    ->money('USD')
                    ->sortable(),

                TextColumn::make('campaign_id')
                    ->label('Campaign ID')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('Not created yet'),

                TextColumn::make('ad_group_name')
                    ->label('Ad Group')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('headline_1')
                    ->label('Headline 1')
                    ->limit(20)
                    ->toggleable(),

                TextColumn::make('headline_2')
                    ->label('Headline 2')
                    ->limit(20)
                    ->toggleable(),

                TextColumn::make('description')
                    ->label('Description')
                    ->limit(30)
                    ->toggleable(),

                TextColumn::make('user.name')
                    ->label('Created By')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(),

                TextColumn::make('created_at_api')
                    ->label('Created in Google Ads')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable()
                    ->placeholder('Not created yet'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'PENDING' => 'Pending',
                        'CREATED' => 'Created',
                        'FAILED' => 'Failed',
                    ]),
                SelectFilter::make('customer_id')
                    ->label('Account')
                    ->relationship('googleAdsAccount', 'name'),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make()
                    ->visible(fn (GoogleAdsCampaign $record): bool => $record->status === 'FAILED'),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
