<?php

namespace App\Filament\Resources\GoogleAdsCampaigns\Tables;

use App\Models\GoogleAdsCampaign;
use App\Services\GoogleAdsService;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Notifications\Notification;

class GoogleAdsCampaignsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('campaign_name')
                    ->label('Campaign Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('googleAdsAccount.name')
                    ->label('Account')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'CREATED' => 'success',
                        'PENDING' => 'warning',
                        'FAILED' => 'danger',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),

                TextColumn::make('daily_budget_dollars')
                    ->label('Daily Budget')
                    ->money('USD')
                    ->sortable(),

                TextColumn::make('cpc_bid_dollars')
                    ->label('Max CPC')
                    ->money('USD')
                    ->sortable(),

                TextColumn::make('campaign_id')
                    ->label('Campaign ID')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('Not created yet'),

                TextColumn::make('ad_group_name')
                    ->label('Ad Group')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('headline_1')
                    ->label('Headline 1')
                    ->limit(20)
                    ->toggleable(),

                TextColumn::make('headline_2')
                    ->label('Headline 2')
                    ->limit(20)
                    ->toggleable(),

                TextColumn::make('description')
                    ->label('Description')
                    ->limit(30)
                    ->toggleable(),

                TextColumn::make('user.name')
                    ->label('Created By')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(),

                TextColumn::make('created_at_api')
                    ->label('Created in Google Ads')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable()
                    ->placeholder('Not created yet'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'PENDING' => 'Pending',
                        'CREATED' => 'Created',
                        'FAILED' => 'Failed',
                    ]),
                SelectFilter::make('customer_id')
                    ->label('Account')
                    ->relationship('googleAdsAccount', 'name'),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make()
                    ->visible(fn (GoogleAdsCampaign $record): bool => $record->status === 'FAILED'),
                Action::make('sync_from_api')
                    ->label('Sync from API')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function (GoogleAdsCampaign $record) {
                        try {
                            $service = app(GoogleAdsService::class);
                            $service->updateCampaignFromApi($record);

                            Notification::make()
                                ->title('Campaign synced successfully')
                                ->body("Campaign '{$record->campaign_name}' has been updated with latest data from Google Ads.")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Sync failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->visible(fn (GoogleAdsCampaign $record): bool => !empty($record->campaign_id)),
                Action::make('view_details')
                    ->label('View API Details')
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->action(function (GoogleAdsCampaign $record) {
                        try {
                            $service = app(GoogleAdsService::class);
                            $details = $service->syncCampaignDetails($record->customer_id, $record->campaign_id);

                            // You could show this in a modal or redirect to a details page
                            Notification::make()
                                ->title('Campaign details retrieved')
                                ->body("Retrieved details for campaign '{$record->campaign_name}'")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to get details')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (GoogleAdsCampaign $record): bool => !empty($record->campaign_id)),
            ])
            ->toolbarActions([
                Action::make('sync_campaigns_from_api')
                    ->label('Sync Campaigns from API')
                    ->icon('heroicon-o-cloud-arrow-down')
                    ->color('info')
                    ->form([
                        \Filament\Forms\Components\Select::make('customer_id')
                            ->label('Google Ads Account')
                            ->options(\App\Models\GoogleAdsAccount::enabled()->pluck('name', 'customer_id'))
                            ->required()
                            ->searchable()
                            ->helperText('Select the account to sync campaigns from'),
                    ])
                    ->action(function (array $data) {
                        try {
                            $service = app(GoogleAdsService::class);
                            $campaigns = $service->syncCampaignsFromApi($data['customer_id']);

                            Notification::make()
                                ->title('Campaigns synced successfully')
                                ->body("Synced {$campaigns->count()} campaigns from Google Ads API")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Sync failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
