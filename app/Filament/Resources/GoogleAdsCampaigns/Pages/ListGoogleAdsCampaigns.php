<?php

namespace App\Filament\Resources\GoogleAdsCampaigns\Pages;

use App\Filament\Resources\GoogleAdsCampaigns\GoogleAdsCampaignResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListGoogleAdsCampaigns extends ListRecords
{
    protected static string $resource = GoogleAdsCampaignResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
