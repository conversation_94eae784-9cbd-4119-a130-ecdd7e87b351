<?php

namespace App\Filament\Resources\GoogleAdsCampaigns\Pages;

use App\Filament\Resources\GoogleAdsCampaigns\GoogleAdsCampaignResource;
use App\Services\GoogleAdsService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class CreateGoogleAdsCampaign extends CreateRecord
{
    protected static string $resource = GoogleAdsCampaignResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Add the current user ID
        $data['user_id'] = Auth::id();
        return $data;
    }

    protected function afterCreate(): void
    {
        try {
            // Get the created record
            $campaign = $this->record;

            // Use the GoogleAdsService to create the campaign
            $service = app(GoogleAdsService::class);

            // Prepare data for the service
            $campaignData = [
                'customer_id' => $campaign->customer_id,
                'campaign_name' => $campaign->campaign_name,
                'ad_group_name' => $campaign->ad_group_name,
                'budget_amount' => $campaign->daily_budget_dollars,
                'cpc_bid' => $campaign->cpc_bid_dollars,
                'keywords' => $campaign->keywords,
                'headline_1' => $campaign->headline_1,
                'headline_2' => $campaign->headline_2,
                'description' => $campaign->description,
                'final_url' => $campaign->final_url,
            ];

            // Create the campaign via Google Ads API
            $updatedCampaign = $service->createSearchCampaign($campaignData);

            // Update our local record with the response
            $this->record = $updatedCampaign;

            Notification::make()
                ->title('Campaign created successfully!')
                ->body("Campaign '{$campaign->campaign_name}' has been created in Google Ads.")
                ->success()
                ->send();

        } catch (\Exception $e) {
            // The campaign record was already created in the database
            // but the Google Ads API call failed
            Notification::make()
                ->title('Campaign creation failed')
                ->body("Failed to create campaign in Google Ads: {$e->getMessage()}")
                ->danger()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
