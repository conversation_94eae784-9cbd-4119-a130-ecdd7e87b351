<?php

namespace App\Filament\Resources\GoogleAdsCampaigns\Schemas;

use App\Models\GoogleAdsAccount;
use App\Services\GoogleAdsService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class GoogleAdsCampaignForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('customer_id')
                    ->label('Google Ads Account')
                    ->options(GoogleAdsAccount::enabled()->pluck('name', 'customer_id'))
                    ->required()
                    ->searchable()
                    ->helperText('Select the Google Ads account where the campaign will be created'),

                TextInput::make('campaign_name')
                    ->label('Campaign Name')
                    ->required()
                    ->maxLength(255)
                    ->helperText('Maximum 255 characters'),

                TextInput::make('ad_group_name')
                    ->label('Ad Group Name')
                    ->required()
                    ->maxLength(255)
                    ->helperText('Maximum 255 characters'),

                TextInput::make('budget_amount')
                    ->label('Daily Budget ($)')
                    ->required()
                    ->numeric()
                    ->minValue(1.00)
                    ->step(0.01)
                    ->prefix('$')
                    ->helperText('Minimum $1.00 per day'),

                TextInput::make('cpc_bid')
                    ->label('Max CPC Bid ($)')
                    ->required()
                    ->numeric()
                    ->minValue(0.01)
                    ->step(0.01)
                    ->prefix('$')
                    ->helperText('Minimum $0.01 per click'),

                Textarea::make('keywords')
                    ->label('Keywords')
                    ->required()
                    ->rows(6)
                    ->placeholder("Enter keywords, one per line:\nkeyword1\n[exact match keyword]\n\"phrase match keyword\"\n+broad match modifier")
                    ->helperText('Use [brackets] for exact match, "quotes" for phrase match, +plus for broad match modifier')
                    ->columnSpanFull(),

                TextInput::make('headline_1')
                    ->label('Headline 1')
                    ->required()
                    ->maxLength(30)
                    ->helperText('Maximum 30 characters'),

                TextInput::make('headline_2')
                    ->label('Headline 2')
                    ->required()
                    ->maxLength(30)
                    ->helperText('Maximum 30 characters'),

                TextInput::make('description')
                    ->label('Description')
                    ->required()
                    ->maxLength(90)
                    ->helperText('Maximum 90 characters'),

                TextInput::make('final_url')
                    ->label('Final URL')
                    ->required()
                    ->url()
                    ->placeholder('https://example.com/landing-page')
                    ->helperText('Must be a valid HTTP or HTTPS URL'),
            ]);
    }
}
