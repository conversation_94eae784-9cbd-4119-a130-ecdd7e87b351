<?php

namespace App\Filament\Resources\GoogleAdsSettings\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class GoogleAdsSettingInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('key'),
                TextEntry::make('type'),
                IconEntry::make('is_encrypted')
                    ->boolean(),
                IconEntry::make('is_required')
                    ->boolean(),
                TextEntry::make('group'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
