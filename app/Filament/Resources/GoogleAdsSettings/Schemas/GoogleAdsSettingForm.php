<?php

namespace App\Filament\Resources\GoogleAdsSettings\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class GoogleAdsSettingForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('key')
                    ->required(),
                Textarea::make('value')
                    ->columnSpanFull(),
                TextInput::make('type')
                    ->required()
                    ->default('string'),
                Textarea::make('description')
                    ->columnSpanFull(),
                Toggle::make('is_encrypted')
                    ->required(),
                Toggle::make('is_required')
                    ->required(),
                TextInput::make('group')
                    ->required()
                    ->default('general'),
            ]);
    }
}
