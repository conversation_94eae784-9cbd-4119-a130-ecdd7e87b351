<?php

namespace App\Filament\Resources\GoogleAdsSettings\Pages;

use App\Filament\Resources\GoogleAdsSettings\GoogleAdsSettingResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditGoogleAdsSetting extends EditRecord
{
    protected static string $resource = GoogleAdsSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
