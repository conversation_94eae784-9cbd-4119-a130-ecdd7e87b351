<?php

namespace App\Filament\Resources\GoogleAdsSettings\Pages;

use App\Filament\Resources\GoogleAdsSettings\GoogleAdsSettingResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListGoogleAdsSettings extends ListRecords
{
    protected static string $resource = GoogleAdsSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
