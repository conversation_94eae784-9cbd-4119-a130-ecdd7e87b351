<?php

namespace App\Filament\Resources\GoogleAdsSettings;

use App\Filament\Resources\GoogleAdsSettings\Pages\CreateGoogleAdsSetting;
use App\Filament\Resources\GoogleAdsSettings\Pages\EditGoogleAdsSetting;
use App\Filament\Resources\GoogleAdsSettings\Pages\ListGoogleAdsSettings;
use App\Filament\Resources\GoogleAdsSettings\Pages\ViewGoogleAdsSetting;
use App\Filament\Resources\GoogleAdsSettings\Schemas\GoogleAdsSettingForm;
use App\Filament\Resources\GoogleAdsSettings\Schemas\GoogleAdsSettingInfolist;
use App\Filament\Resources\GoogleAdsSettings\Tables\GoogleAdsSettingsTable;
use App\Models\GoogleAdsSetting;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class GoogleAdsSettingResource extends Resource
{
    protected static ?string $model = GoogleAdsSetting::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function form(Schema $schema): Schema
    {
        return GoogleAdsSettingForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return GoogleAdsSettingInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return GoogleAdsSettingsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListGoogleAdsSettings::route('/'),
            'create' => CreateGoogleAdsSetting::route('/create'),
            'view' => ViewGoogleAdsSetting::route('/{record}'),
            'edit' => EditGoogleAdsSetting::route('/{record}/edit'),
        ];
    }
}
