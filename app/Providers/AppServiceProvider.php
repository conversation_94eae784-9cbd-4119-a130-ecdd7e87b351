<?php

namespace App\Providers;

use App\Services\GoogleAdsService;
use App\Repositories\GoogleAdsRepository;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(GoogleAdsRepository::class);
        $this->app->singleton(GoogleAdsService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
