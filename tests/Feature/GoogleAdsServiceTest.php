<?php

use App\Models\GoogleAdsAccount;
use App\Models\GoogleAdsSetting;
use App\Models\AuditLog;
use App\Models\User;
use App\Services\GoogleAdsService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a test user
    $this->actingAs(User::factory()->create());

    // Seed basic settings
    $this->artisan('db:seed', ['--class' => 'GoogleAdsSettingsSeeder']);
});

test('can check configuration status', function () {
    $service = app(GoogleAdsService::class);

    // Initially should not be configured
    expect($service->isConfigured())->toBeFalse();

    // Add required settings
    GoogleAdsSetting::setValue('developer_token', 'test_token');
    GoogleAdsSetting::setValue('login_customer_id', '**********');
    GoogleAdsSetting::setValue('oauth2_client_id', 'test_client_id');
    GoogleAdsSetting::setValue('oauth2_client_secret', 'test_secret');
    GoogleAdsSetting::setValue('oauth2_refresh_token', 'test_token');

    // Now should be configured
    expect($service->isConfigured())->toBeTrue();
});

test('can get configuration status', function () {
    $service = app(GoogleAdsService::class);

    $status = $service->getConfigurationStatus();

    expect($status)->toBeArray()
        ->toHaveKey('developer_token')
        ->toHaveKey('login_customer_id');
    expect($status['developer_token']['configured'])->toBeFalse();
});

test('can create google ads account model', function () {
    $account = GoogleAdsAccount::create([
        'customer_id' => '**********',
        'name' => 'Test Account',
        'descriptive_name' => 'Test Account Description',
        'is_manager_account' => false,
        'status' => 'ENABLED',
        'currency_code' => 'USD',
        'time_zone' => 'America/New_York',
        'can_manage_clients' => false,
        'test_account' => true,
    ]);

    expect($account->isEnabled())->toBeTrue();
    expect($account->isMcc())->toBeFalse();
    expect($account->formatted_customer_id)->toBe('123-456-7890');

    $this->assertDatabaseHas('google_ads_accounts', [
        'customer_id' => '**********',
        'name' => 'Test Account',
        'status' => 'ENABLED',
    ]);
});

test('can create audit log', function () {
    $log = AuditLog::logAction(
        'test_action',
        'Test action performed',
        GoogleAdsAccount::class,
        '1',
        '**********'
    );

    expect($log->getStatusColor())->toBe('success');

    $this->assertDatabaseHas('audit_logs', [
        'action' => 'test_action',
        'description' => 'Test action performed',
        'model_type' => GoogleAdsAccount::class,
        'customer_id' => '**********',
        'status' => 'success',
    ]);
});

test('can manage google ads settings', function () {
    // Test setting a value
    $setting = GoogleAdsSetting::setValue('test_key', 'test_value', [
        'type' => 'string',
        'description' => 'Test setting',
        'group' => 'test',
    ]);

    expect(GoogleAdsSetting::getValue('test_key'))->toBe('test_value');

    // Test encrypted setting
    $encryptedSetting = GoogleAdsSetting::setValue('encrypted_key', 'secret_value', [
        'type' => 'encrypted',
        'is_encrypted' => true,
    ]);

    expect($encryptedSetting->decrypted_value)->toBe('secret_value');
    expect($encryptedSetting->value)->not->toBe('secret_value');
});

test('google ads account relationships work correctly', function () {
    // Create parent MCC account
    $mccAccount = GoogleAdsAccount::create([
        'customer_id' => '**********',
        'name' => 'MCC Account',
        'is_manager_account' => true,
        'status' => 'ENABLED',
    ]);

    // Create child account
    $childAccount = GoogleAdsAccount::create([
        'customer_id' => '**********',
        'name' => 'Child Account',
        'is_manager_account' => false,
        'status' => 'ENABLED',
        'manager_customer_id' => '**********',
    ]);

    // Test relationships
    expect($mccAccount->isMcc())->toBeTrue();
    expect($childAccount->isMcc())->toBeFalse();
    expect($childAccount->manager->customer_id)->toBe($mccAccount->customer_id);
    expect($mccAccount->childAccounts->contains($childAccount))->toBeTrue();
});
