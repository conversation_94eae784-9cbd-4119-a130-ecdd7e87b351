<?php

use App\Models\GoogleAdsAccount;
use App\Models\GoogleAdsCampaign;
use App\Models\User;
use App\Services\GoogleAdsService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a test user
    $this->testUser = User::factory()->create();
    $this->actingAs($this->testUser);

    // Seed basic settings
    $this->artisan('db:seed', ['--class' => 'GoogleAdsSettingsSeeder']);

    // Create a test Google Ads account
    $this->testAccount = GoogleAdsAccount::create([
        'customer_id' => '**********',
        'name' => 'Test Account',
        'descriptive_name' => 'Test Account Description',
        'is_manager_account' => false,
        'status' => 'ENABLED',
        'currency_code' => 'USD',
        'time_zone' => 'America/New_York',
        'can_manage_clients' => false,
        'test_account' => true,
    ]);
});

test('can create google ads campaign model', function () {
    $campaign = GoogleAdsCampaign::create([
        'customer_id' => $this->testAccount->customer_id,
        'campaign_name' => 'Test Campaign',
        'ad_group_name' => 'Test Ad Group',
        'daily_budget_micros' => 5000000, // $5.00
        'cpc_bid_micros' => 1500000, // $1.50
        'keywords' => [
            ['text' => 'test keyword', 'match_type' => 'BROAD'],
            ['text' => 'exact keyword', 'match_type' => 'EXACT'],
        ],
        'headline_1' => 'Test Headline 1',
        'headline_2' => 'Test Headline 2',
        'description' => 'Test description for the ad',
        'final_url' => 'https://example.com',
        'status' => 'PENDING',
        'user_id' => $this->testUser->id,
    ]);

    expect($campaign->daily_budget_dollars)->toBe(5.0);
    expect($campaign->cpc_bid_dollars)->toBe(1.5);
    expect($campaign->isPending())->toBeTrue();
    expect($campaign->getStatusColor())->toBe('warning');
    
    $this->assertDatabaseHas('google_ads_campaigns', [
        'campaign_name' => 'Test Campaign',
        'customer_id' => $this->testAccount->customer_id,
        'status' => 'PENDING',
    ]);
});

test('campaign belongs to google ads account', function () {
    $campaign = GoogleAdsCampaign::create([
        'customer_id' => $this->testAccount->customer_id,
        'campaign_name' => 'Test Campaign',
        'ad_group_name' => 'Test Ad Group',
        'daily_budget_micros' => 5000000,
        'cpc_bid_micros' => 1500000,
        'keywords' => [['text' => 'test', 'match_type' => 'BROAD']],
        'headline_1' => 'Test Headline 1',
        'headline_2' => 'Test Headline 2',
        'description' => 'Test description',
        'final_url' => 'https://example.com',
        'status' => 'PENDING',
        'user_id' => $this->testUser->id,
    ]);

    expect($campaign->googleAdsAccount->name)->toBe('Test Account');
    expect($campaign->googleAdsAccount->customer_id)->toBe($this->testAccount->customer_id);
});

test('can validate campaign data', function () {
    $service = app(GoogleAdsService::class);
    
    // Test missing required fields
    expect(fn () => $service->createSearchCampaign([]))
        ->toThrow(\InvalidArgumentException::class);
    
    // Test invalid budget amount
    expect(fn () => $service->createSearchCampaign([
        'customer_id' => $this->testAccount->customer_id,
        'campaign_name' => 'Test',
        'ad_group_name' => 'Test',
        'budget_amount' => 0.50, // Too low
        'cpc_bid' => 1.00,
        'keywords' => 'test',
        'headline_1' => 'Test',
        'headline_2' => 'Test',
        'description' => 'Test',
        'final_url' => 'https://example.com',
    ]))->toThrow(\InvalidArgumentException::class);
    
    // Test invalid URL
    expect(fn () => $service->createSearchCampaign([
        'customer_id' => $this->testAccount->customer_id,
        'campaign_name' => 'Test',
        'ad_group_name' => 'Test',
        'budget_amount' => 5.00,
        'cpc_bid' => 1.00,
        'keywords' => 'test',
        'headline_1' => 'Test',
        'headline_2' => 'Test',
        'description' => 'Test',
        'final_url' => 'invalid-url',
    ]))->toThrow(\InvalidArgumentException::class);
});

test('can parse keywords correctly', function () {
    $service = app(GoogleAdsService::class);
    
    // Use reflection to access the protected method
    $reflection = new ReflectionClass($service);
    $method = $reflection->getMethod('parseKeywords');
    $method->setAccessible(true);
    
    $keywordsText = "broad keyword\n[exact keyword]\n\"phrase keyword\"\n+broad modifier";
    $parsed = $method->invoke($service, $keywordsText);
    
    expect($parsed)->toHaveCount(4);
    expect($parsed[0])->toBe(['text' => 'broad keyword', 'match_type' => 'BROAD']);
    expect($parsed[1])->toBe(['text' => 'exact keyword', 'match_type' => 'EXACT']);
    expect($parsed[2])->toBe(['text' => 'phrase keyword', 'match_type' => 'PHRASE']);
    expect($parsed[3])->toBe(['text' => 'broad modifier', 'match_type' => 'BROAD_MODIFIED']);
});

test('campaign status methods work correctly', function () {
    $pendingCampaign = GoogleAdsCampaign::factory()->make(['status' => 'PENDING']);
    $createdCampaign = GoogleAdsCampaign::factory()->make(['status' => 'CREATED', 'campaign_id' => '12345']);
    $failedCampaign = GoogleAdsCampaign::factory()->make(['status' => 'FAILED']);

    expect($pendingCampaign->isPending())->toBeTrue();
    expect($pendingCampaign->isCreated())->toBeFalse();
    expect($pendingCampaign->isFailed())->toBeFalse();

    expect($createdCampaign->isPending())->toBeFalse();
    expect($createdCampaign->isCreated())->toBeTrue();
    expect($createdCampaign->isFailed())->toBeFalse();

    expect($failedCampaign->isPending())->toBeFalse();
    expect($failedCampaign->isCreated())->toBeFalse();
    expect($failedCampaign->isFailed())->toBeTrue();
});

test('can create campaign from api data', function () {
    $service = app(GoogleAdsService::class);

    // Mock API campaign data
    $apiCampaignData = [
        'campaign_id' => '12345',
        'campaign_name' => 'API Test Campaign',
        'status' => 'ENABLED',
        'daily_budget_micros' => 10000000, // $10.00
        'ad_groups' => [
            [
                'ad_group_id' => '67890',
                'ad_group_name' => 'API Test Ad Group',
                'cpc_bid_micros' => 2000000, // $2.00
                'keywords' => [
                    ['text' => 'api keyword', 'match_type' => 'BROAD'],
                    ['text' => 'test keyword', 'match_type' => 'EXACT'],
                ],
                'ads' => [
                    [
                        'headlines' => ['API Headline 1', 'API Headline 2'],
                        'descriptions' => ['API Description'],
                        'final_urls' => ['https://api-example.com'],
                    ]
                ]
            ]
        ]
    ];

    // Use reflection to access the protected method
    $reflection = new ReflectionClass($service);
    $method = $reflection->getMethod('createCampaignFromApiData');
    $method->setAccessible(true);

    $campaign = $method->invoke($service, $this->testAccount->customer_id, $apiCampaignData);

    expect($campaign->campaign_id)->toBe('12345');
    expect($campaign->campaign_name)->toBe('API Test Campaign');
    expect($campaign->status)->toBe('CREATED');
    expect($campaign->daily_budget_dollars)->toBe(10.0);
    expect($campaign->cpc_bid_dollars)->toBe(2.0);
    expect($campaign->headline_1)->toBe('API Headline 1');
    expect($campaign->headline_2)->toBe('API Headline 2');
    expect($campaign->description)->toBe('API Description');
    expect($campaign->final_url)->toBe('https://api-example.com');
    expect($campaign->keywords)->toHaveCount(2);
});

test('can map api status to local status', function () {
    $service = app(GoogleAdsService::class);

    // Use reflection to access the protected method
    $reflection = new ReflectionClass($service);
    $method = $reflection->getMethod('mapApiStatusToLocal');
    $method->setAccessible(true);

    expect($method->invoke($service, 'ENABLED'))->toBe('CREATED');
    expect($method->invoke($service, 'PAUSED'))->toBe('CREATED');
    expect($method->invoke($service, 'REMOVED'))->toBe('FAILED');
    expect($method->invoke($service, 'UNKNOWN'))->toBe('CREATED');
});
