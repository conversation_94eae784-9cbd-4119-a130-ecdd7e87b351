# Google Ads Admin Dashboard

A comprehensive Laravel 11 admin dashboard built with Filament 4 for managing Google Ads accounts via the Google Ads API v20.

## Features

### ✅ Implemented Features

1. **Google Ads Account Management**
   - List all child accounts under MCC using GAQL
   - Display account ID, name, manager status, and current status
   - Create new accounts via `CustomerServiceClient::createCustomerClient()`
   - Enable/disable accounts by pausing/unpausing campaigns
   - Show MCC vs regular account distinction
   - Formatted customer ID display (123-456-7890)

2. **Authentication & Configuration**
   - Google Ads API authentication using `google_ads_php.ini` configuration
   - Settings management for developer token, login customer ID, OAuth2 credentials
   - Encrypted storage of sensitive credentials
   - Configuration validation and status checking

3. **User Management Simulation**
   - Simulate user invitation/sharing access (UI only, as Google Ads API doesn't support direct invitations)
   - Role-based access simulation (Admin, Standard, Read Only)

4. **Audit Logging**
   - Complete audit trail for all actions
   - Track user actions, API calls, and system events
   - Detailed logging with metadata and error tracking

5. **Filament Admin Interface**
   - Modern, responsive admin dashboard
   - Advanced filtering and searching
   - Bulk actions and individual record actions
   - Real-time notifications for operations

### 🏗️ Architecture

- **Service/Repository Pattern**: Clean separation of concerns
- **GoogleAdsService**: High-level business logic
- **GoogleAdsRepository**: Direct API interactions
- **Eloquent Models**: Database abstraction with relationships
- **Filament Resources**: Admin interface components

### 📁 Project Structure

```
app/
├── Models/
│   ├── GoogleAdsAccount.php      # Account model with relationships
│   ├── GoogleAdsSetting.php      # Configuration settings model
│   └── AuditLog.php              # Audit logging model
├── Services/
│   └── GoogleAdsService.php      # Business logic layer
├── Repositories/
│   └── GoogleAdsRepository.php   # Google Ads API interactions
└── Filament/Resources/
    ├── GoogleAdsAccounts/        # Account management interface
    ├── GoogleAdsSettings/        # Settings management interface
    └── AuditLogs/               # Audit log viewing interface

database/migrations/
├── create_google_ads_accounts_table.php
├── create_google_ads_settings_table.php
└── create_audit_logs_table.php

config/
└── google_ads.php               # Google Ads configuration
```

## Installation & Setup

### 1. Dependencies
Already installed:
- Laravel 11
- Filament 4
- Google Ads PHP SDK v28

### 2. Database Setup
```bash
php artisan migrate
php artisan db:seed --class=GoogleAdsSettingsSeeder
```

### 3. Admin User
```bash
php artisan make:filament-user
```

### 4. Configuration
1. Access the admin panel at `/app`
2. Navigate to Google Ads > Settings
3. Configure your Google Ads API credentials:
   - Developer Token
   - Login Customer ID (MCC)
   - OAuth2 Client ID
   - OAuth2 Client Secret
   - OAuth2 Refresh Token

### 5. Environment Variables (Optional)
```env
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_mcc_customer_id
GOOGLE_ADS_OAUTH2_CLIENT_ID=your_client_id
GOOGLE_ADS_OAUTH2_CLIENT_SECRET=your_client_secret
GOOGLE_ADS_OAUTH2_REFRESH_TOKEN=your_refresh_token
```

## Usage

### Account Management
1. **Sync Accounts**: Click "Sync All Accounts" to fetch accounts from Google Ads API
2. **Create Account**: Use the create form to add new Google Ads accounts
3. **Enable/Disable**: Use row actions to pause/unpause campaigns
4. **Invite Users**: Simulate user invitations with email and role selection

### Settings Management
- Configure API credentials securely
- Set default currency and timezone
- Enable/disable auto-sync functionality

### Audit Logs
- View all system activities
- Filter by action type, user, or date
- Track API calls and their results

## API Integration

### Google Ads API v20 Features Used
- **Customer Service**: Account creation and management
- **Google Ads Service**: GAQL queries for account listing
- **Campaign Service**: Campaign status management for account disable/enable

### GAQL Queries
```sql
-- Get child accounts
SELECT 
    customer_client.client_customer,
    customer_client.level,
    customer_client.manager,
    customer_client.descriptive_name,
    customer_client.currency_code,
    customer_client.time_zone,
    customer_client.id,
    customer_client.status,
    customer_client.test_account
FROM customer_client 
WHERE customer_client.level <= 1

-- Get campaigns for status updates
SELECT campaign.id, campaign.name 
FROM campaign 
WHERE campaign.status != 'REMOVED'
```

## Security Features

- **Encrypted Settings**: Sensitive data encrypted in database
- **Audit Logging**: Complete activity tracking
- **User Authentication**: Filament's built-in authentication
- **Input Validation**: Form validation and sanitization

## Future Enhancements

### 🔄 Planned Features
1. **MCC Tree Visualization**: Recursive hierarchy display
2. **Campaign Management**: Direct campaign operations
3. **Performance Metrics**: Account performance dashboards
4. **Automated Sync**: Scheduled account synchronization
5. **Email Notifications**: Real user invitation system
6. **API Rate Limiting**: Request throttling and queuing
7. **Multi-tenant Support**: Multiple MCC management

### 🧪 Testing
- Unit tests for services and repositories
- Feature tests for Filament resources
- API integration tests

## Troubleshooting

### Common Issues
1. **API Authentication**: Ensure all OAuth2 credentials are correct
2. **Customer ID Format**: Use 10-digit format without dashes in API calls
3. **Permissions**: Verify MCC has access to child accounts
4. **Rate Limits**: Implement proper request throttling

### Debug Mode
Enable detailed logging in `config/google_ads.php`:
```php
'logging' => [
    'enabled' => true,
    'level' => 'debug',
],
```

## License
This project is built on Laravel and uses the Google Ads API under their respective licenses.
